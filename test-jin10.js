#!/usr/bin/env node

// Jin10 API 测试脚本
// 使用方法: node test-jin10.js

import fs from 'fs/promises';
import path from 'path';

const HOST = 'http://127.0.0.1:8123';
const ENDPOINT = '/api/v1/jin10';
const URL = `${HOST}${ENDPOINT}`;

async function testJin10API() {
    console.log('=== 测试 Jin10 API ===');
    console.log(`URL: ${URL}`);
    console.log(`时间: ${new Date().toLocaleString()}`);
    console.log('');

    try {
        console.log('发送请求中...');
        const startTime = Date.now();
        
        const response = await fetch(URL, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
        });

        const endTime = Date.now();
        const duration = endTime - startTime;

        console.log(`HTTP 状态码: ${response.status}`);
        console.log(`请求耗时: ${duration}ms`);
        console.log('');

        if (response.ok) {
            console.log('✅ 请求成功!');
            
            const result = await response.json();
            
            // 保存响应到文件
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
            const outputFile = `jin10_response_${timestamp}.json`;
            await fs.writeFile(outputFile, JSON.stringify(result, null, 2));
            
            console.log('');
            console.log('=== 响应数据摘要 ===');
            console.log(`错误状态: ${result.err}`);
            console.log(`消息: ${result.msg}`);
            console.log(`总新闻数量: ${result.data?.length || 0}`);
            
            if (result.data && result.data.length > 0) {
                const cryptoNews = result.data.filter(item => item.type === 'crypto');
                const techNews = result.data.filter(item => item.type === 'tech');
                
                console.log(`加密货币新闻: ${cryptoNews.length}`);
                console.log(`科技新闻: ${techNews.length}`);
                console.log('');
                
                console.log('=== 最新新闻示例 ===');
                const latestNews = result.data[0];
                console.log(`ID: ${latestNews.id}`);
                console.log(`时间: ${latestNews.time}`);
                console.log(`标题: ${latestNews.title}`);
                console.log(`类型: ${latestNews.type}`);
                console.log(`热度: ${latestNews.hot}`);
                console.log(`内容长度: ${latestNews.content?.length || 0} 字符`);
                console.log(`截图: ${latestNews.screenshot}`);
                
                if (latestNews.content) {
                    const preview = latestNews.content.length > 100 
                        ? latestNews.content.substring(0, 100) + '...'
                        : latestNews.content;
                    console.log(`内容预览: ${preview}`);
                }
            } else {
                console.log('当前没有符合条件的新闻（可能没有"爆"级别的新闻）');
            }
            
            console.log('');
            console.log(`📁 完整响应已保存到: ${outputFile}`);
            
        } else {
            console.log('❌ 请求失败!');
            const errorText = await response.text();
            console.log('错误响应:', errorText);
        }

    } catch (error) {
        console.error('❌ 请求出错:', error.message);
        if (error.cause) {
            console.error('错误原因:', error.cause);
        }
    }

    console.log('');
    console.log('=== 测试完成 ===');
}

// 运行测试
testJin10API();
