services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      # Add build caching for faster builds
      cache_from:
        - ${DOCKER_REGISTRY:-local}/app:latest
    ports:
      - '8123:8123'
    env_file:
      - .env
    command: ['tsx', 'main.ts']
    # Use bind mounts for better performance
    volumes:
      - ./plugins:/app/plugins:ro
      - ./public:/app/public
      - ./routes:/app/routes:ro
      - ./schemas:/app/schemas:ro
      - ./services:/app/services:ro
      - ./main.ts:/app/main.ts
    # Add health check
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8123/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    # Add resource constraints
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
    # Restart policy for better availability
    # restart: unless-stopped
