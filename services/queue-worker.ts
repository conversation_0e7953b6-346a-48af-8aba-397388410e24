declare module 'fastify' {
  interface FastifyInstance {
    worker: <PERSON><PERSON><PERSON><PERSON><PERSON>;
  }
}

import { type Job, Worker } from 'bullmq';
import type { SteelService } from './browse';
import { log } from '../services/log';
import type { PlaywrightService } from './playwright';
import { HKEXService } from './hkex';
import type Redis from 'ioredis';

export class QueueWorker {
  private worker: Worker;
  private connection: Redis;
  private browser: SteelService;
  private playwright: PlaywrightService;
  private hkexService: HKEXService;
  redis: Redis;

  constructor(
    browser: SteelService,
    playwright: PlaywrightService,
    redis: Redis
  ) {
    this.browser = browser;
    this.playwright = playwright;
    this.redis = redis;
    this.hkexService = HKEXService.getInstance();

    this.connection = redis;

    this.worker = new Worker(
      process.env.QUEUE_NAME || 'request-queue',
      this.processJob.bind(this),
      {
        connection: this.connection,
        concurrency: 1,
        stalledInterval: 30000,
        lockDuration: 90000,
      }
    );

    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    this.worker.on('active', (job) => {
      log.info(
        `Job ${job.id} started processing at ${new Date().toISOString()}`
      );
    });

    this.worker.on('completed', (job, result) => {
      log.info(
        `Job ${job.id} completed successfully at ${new Date().toISOString()}`
      );
    });

    this.worker.on('failed', (job, error) => {
      log.error(
        `Job ${job?.id} failed at ${new Date().toISOString()}:`,
        error.message
      );
    });

    this.worker.on('error', (error) => {
      log.error('Worker error:');
      log.error(error);
    });

    this.worker.on('stalled', (jobId) => {
      console.warn(`Job ${jobId} stalled at ${new Date().toISOString()}`);
    });
  }

  private async processJob(job: Job): Promise<unknown> {
    try {
      log.info(`Processing job ${job.id} with URL: ${job.data.url}`);

      let screenshot;

      switch (job.data.channel) {
        case 'twitter':
          // screenshot = await this.browser.screenshot({ url: job.data.url });
          screenshot = await this.browser.screenshotTwitter({
            url: job.data.url,
          });
          break;
        case 'truthsocial':
          screenshot = await this.browser.screenshotTruthSocial({
            url: job.data.url,
          });
          break;
        case 'hkex':
          return await this.processHKEXJob(job);
        default:
          break;
      }

      const result = {
        url: job.data.url,
        screenshot,
        completedAt: new Date().toISOString(),
        jobId: job.id,
      };

      log.info(`Job ${job.id} completed successfully`);
      return result;
    } catch (error) {
      log.error(`Error processing job ${job.id}:`);
      log.error(error);
      throw error; // This will mark the job as failed
    }
  }

  private async processHKEXJob(job: Job): Promise<unknown> {
    try {
      log.info(`Processing HKEX job ${job.id}`);
      
      // Create a page using playwright
      const context = await this.playwright.createContext();
      const page = await context.newPage();
      
      try {
        // Scrape news
        const news = await this.hkexService.scrapeNewsReleases(page);
        
        let screenshot: string | undefined = undefined;
        if (job.data.screenshot) {
          screenshot = await this.hkexService.takeScreenshot(page);
        }
        
        const result: any = {
          news,
          scrapedAt: new Date().toISOString(),
          jobId: job.id,
        };
        
        // Only include screenshot if it was requested and available
        if (screenshot) {
          result.screenshot = screenshot;
        }
        
        log.info(`HKEX job ${job.id} completed successfully with ${news.length} news items`);
        return result;
      } finally {
        await context.close();
      }
    } catch (error) {
      log.error(`Error processing HKEX job ${job.id}:`, error);
      throw error;
    }
  }

  getStatus() {
    return {
      isRunning: !this.worker.closing,
      name: this.worker.name,
      opts: {
        concurrency: this.worker.opts?.concurrency || 1,
        stalledInterval: this.worker.opts?.stalledInterval || 30000,
        lockDuration: this.worker.opts?.lockDuration || 90000,
      },
    };
  }

  async close() {
    log.info('Closing worker...');
    await this.worker.close();
    await this.connection.quit();
  }

  updateBrowser(browser: SteelService) {
    log.info('Updating browser reference in queue worker');
    this.browser = browser;
  }

  updatePlaywright(playwright: PlaywrightService) {
    log.info('Updating playwright reference in queue worker');
    this.playwright = playwright;
  }
}
