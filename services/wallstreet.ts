import { <PERSON>rowser, Browser<PERSON>ontext, chromium } from 'playwright';
import { log } from './log';

export class Wallstreet {
  private static browser: Browser;
  private static context: BrowserContext;
  private static screenshotDir: string;

  public static async init() {
    this.screenshotDir = 'public/screenshots';
    this.browser = await chromium.launch({
      headless: true,
      // Comprehensive args to simulate real Chrome browser and avoid detection
      args: [
        '--disable-dev-shm-usage', // Prevents OOM in limited memory environments
        '--no-sandbox', // Required in some restricted environments
        '--disable-setuid-sandbox',
        '--disable-gpu', // Reduces resource usage
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-blink-features=AutomationControlled',
        '--disable-infobars',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-images',
        '--disable-default-apps',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=TranslateUI',
        '--disable-ipc-flooding-protection',
        '--no-first-run',
        '--no-default-browser-check',
        '--no-pings',
        '--password-store=basic',
        '--use-mock-keychain',
        '--disable-component-extensions-with-background-pages',
        '--disable-background-networking',
        '--disable-sync',
        '--metrics-recording-only',
        '--disable-default-apps',
        '--mute-audio',
        '--no-zygote',
        '--disable-background-mode',
        '--disable-hang-monitor',
        '--disable-prompt-on-repost',
        '--disable-client-side-phishing-detection',
        '--disable-component-update',
        '--disable-domain-reliability',
        '--disable-features=AudioServiceOutOfProcess',
        '--disable-features=VizDisplayCompositor',
        '--disable-features=VizHitTestSurfaceLayer',
        '--disable-features=VizSurfaceDisplayCompositor',
        '--disable-crash-reporter',
        '--disable-logging',
        '--disable-dev-tools',
        '--disable-web-security',
        '--allow-running-insecure-content',
        '--ignore-certificate-errors',
        '--ignore-ssl-errors',
        '--ignore-certificate-errors-spki-list',
        '--ignore-certificate-errors-ssl-info',
        '--disable-popup-blocking',
        '--disable-notifications',
        '--disable-geolocation',
        '--lang=en-US,en;q=0.9',
      ],
    });
    this.context = await this.browser.newContext({
      viewport: { width: 1920, height: 1080 },
      userAgent:
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      acceptDownloads: true,
      ignoreHTTPSErrors: true,
      javaScriptEnabled: true,
      locale: 'en-US',
      timezoneId: 'America/New_York',
      geolocation: { longitude: -74.006, latitude: 40.7128 },
      permissions: ['geolocation'],
      colorScheme: 'light',
      deviceScaleFactor: 1,
      hasTouch: false,
      isMobile: false,
      extraHTTPHeaders: {
        Accept:
          'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        DNT: '1',
        Connection: 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
      },
    });
  }

  public static async screenshot({ id, uri }: { id: string; uri: string }) {
    if (!this.browser || !this.context) {
      log.error('Browser session not exist or expired.');
      await this.init();
    }
    const page = await this.context.newPage();

    await page.goto(uri, {
      waitUntil: 'domcontentloaded',
    });
    await page.waitForTimeout(500);
    await page.waitForSelector(`.detail-wrap`);

    // remove elements
    await page.evaluate(() => {
      const top = document.querySelector('div.top');
      const main = document.querySelector('div.main');
      if (!main) return;

      const articles = main.querySelectorAll('article');
      const lastArticle = articles[articles.length - 1];
      if (!lastArticle) return;

      let next = lastArticle.nextElementSibling;
      while (next) {
        const toRemove = next;
        next = next.nextElementSibling;
        toRemove.remove();
      }

      if (top && main) {
        const wrapper = document.createElement('div');
        wrapper.id = 'capture-wrapper';
        wrapper.setAttribute(
          'style',
          `
            background-color: #fff;
            padding: 0 60px 36px;
          `
        );

        top.parentNode?.insertBefore(wrapper, top);
        wrapper.appendChild(top);
        wrapper.appendChild(main);
      }
    });

    const wrapper = await page.$('#capture-wrapper');
    if (wrapper) {
      await wrapper.screenshot({ path: `${this.screenshotDir}/${id}.png` });
    }
    await page.close();
  }

  public static formatTime(timestamp: number) {
    const date = new Date(timestamp * 1000);
    return date.toLocaleString('sv-SE', {
      timeZone: 'Asia/Shanghai',
    });
  }

  private static timestampToISO(timestamp) {
    return (
      new Date(timestamp * 1000)
        .toLocaleString('sv-SE', {
          timeZone: 'Asia/Shanghai',
        })
        .replace(' ', 'T') + '.000+08:00'
    );
  }

  public static getHTML({
    id,
    timestamp,
    title,
    contentHtml,
    contentMoreHtml,
  }: {
    id: string;
    timestamp: number;
    title: string;
    contentHtml: string;
    contentMoreHtml: string;
  }) {
    const formattedTime = this.formatTime(timestamp);
    const isoTime = this.timestampToISO(timestamp);
    const template = `<div data-v-c1a393be="" data-v-41d6eebc="" class="detail-wrap" id="${id}">
      <div data-v-c1a393be="" data-v-41d6eebc="" class="top">
        <img
          data-v-c1a393be=""
          data-v-41d6eebc=""
          src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFwAAAAoCAMAAABEgm29AAAB6VBMVEUAAAA9mPo2o/g3lPZDnP8zffE0fPI2o/Y0fPI2pfczffE3pfg3pfgzevI0fPI2pfczffM4pvk4pPo2gfQ1g/M2n/c0gPE0e/E1pPc2ofc0gPE0evI5rP85gPlAoP82ovc3pvg1hPM2nfY1hPI0fPA3pvg4n/czgvI4qPg4pvg2ffI2pfg4nvY1hPM5pvs2gvL///8zf/E1lPQ1oPY1mPU0jPM0hvI0h/IzffE0g/I1nfY1o/Y0jvM0iPM1m/UzgfI1lfU2pPc1mvU0kPQzhPI0kvQzevE0kfQ1j/Q0i/M1l/UzfPE0ivM1n/YzfvA1nPY0i/L4+/7n8/01ovczgPL9/v662/s2pvczefHt9v5Pp/Y0h/Py+P5Pqfc5nvb7/f7E4PyJxPmAwfllqfa12vs9mfVIj/Pi7v3a6fzD3Pu92fux1vuz0vqq0Pqmz/qbxvl4rfZOpvY1pfZEnfXp8v3f7v3T6f3N5PzJ4/zK3/uYzPqcyflqqfZapvZZoPVJnPX1+f6Xwfh+u/iJuPd1tvd8s/dos/dgr/dRrfdNo/ZiovVdofVWnfU8lfRMlvNAjfPR4/y/3/yq1vuj0/udzPmnyfmHv/l3vviLvfhrufhxsvdvpvVRm/Q7kPOtzfmSx/ltufhApPVDlvT/ote0AAAAMHRSTlMAENwmA/D77qinjYz72lpYQD4tLff13cC/mJhzFBQM+u7iy8vBvpybcnJycVdXPz3KB49qAAAFWElEQVRIx6WWZ1sTQRSFEey99967G0CImMRoMKRBEgWDJcFIggpIohEbTaVIx977L/WeOzO7O/DNXD+Y3Ye8HM7ec3ZK5s68E1v37VlfW1VVVV1d7aaJBIOnMa6Ay1dXd7HuYnl5ubfc6/WGQpWVlRcueDxRTw1NfX19RcXlszTnaRKJ0jnodYfWn6GpBRx0ZjM94HIFfEQHm/leYoMejXokvAJDaMI/SCSWzULvXHoOQ/AqjNsNeiwWVMJpLtKAXA7hYJP0qAVn6RB+PrFGZ5dtbHQQGcotWyKQDriwpY7YGGUL4FDOdBaOf6R893yNfWqBw+Eg+jliM5wGcKE8QHA2nUzxCluYDrZpuaRD+hLdbsE+J4XXEpx8iWgP1EcPFHTgzQcatXyBLaw9kVhlZ+/YKNmKXnVdKI9FlHLAYQvPXFuEcNDJluXz7PCtzgZHI+gSDl/wQOFKkNhkOp4o4JKNdRHS7b7IVTygmeJwOh1EB1p5fp3oeKAxqTzgquNVzLa+HOrLmmxadAE3N5GUH7bDF5Nwmm+GNp3kC5Qrz6F86LaBufGsIG2JQnnyEt17Zwp/UGZ3fKEQ/kKHD1ZHaCSbPS/cNNRcKpjKPcw2Ppl7rsXzaNjJ0u/q8H43PI/xnouEgq3mienLNWYbSfJF7LkWz/0NTpb+MK6miX74asTqFhJOnn9k6Mehjqv4MMJwoRs/XnFZ2WKP57yFEK52EfEfj5OtE9gWShHYIqE3iHEP+zIA+i+whW5cvuFlwQNdZI/nyjCECzjYtZNEeT7udr/I5XK9UN6by/X0DPQR4uEUZyhDH6/wAwU785YunyFErFyL5+Kws0HQRYimHxpGfJxS1EnfuUPw/jh96PJ9hnBRih30sQO23Cf2ldQjuvzCyuG5PZ7zFwDucDoaRYZmsG15BPQ7rJ84PYUb7QHfcFdXV6voc/jwUrLfpFL0y5v+qD7X4rk6DFugXMCht1PUYgZag1iiRz/sff6e7rSMVVaCfTNV84ouu80+32x3ZSm74mwEnSYPZ2dEn0+S9Bs9dOP2X1ufZ7uwHWnF9tSk6TJj9vkRO3xFmNdcxr8NK9Gr+rzdEIGctPX5Vw5pJsTsJykqrgx+l9nn9niWhcPmthAdHvw0+7yf2S2tVp/3PcGdpmeh0BixH4Nd0013Xqk+1+K5Lewn4WoVUS/xNqvPXwM1bPb5QEcTZ3+EKvcR7Wv6Fgb30re+sC16PDf5lXJk6A793Aerz/PM6lV9/vI5LuPvs9S52RZDn7eiz09q8QS8QfZ5noWbfT4RNzBPRZ9PdfDVzTHuc6yINrd40fV4+lm5qMUz3RBu9vk01hna4Xkg8Jgd+YqEEj09Gz5K7NnxBFx1Sx6wNvWCnoFH7fcgFp73gPC44JVv/8I1nmQyif0aGR0VCdXjaRPueIqvq1doBFd3fnBM+wKBVvzf0n5FTVY1bhKVWC9f0Ho8/aTcKaW34RkNUiuy9E58aToYw3Ledbnu6SZcMt9zn/Cn1RAbfa7HE66EZZ8PQhu5wg/0BS849XkrvOp3dWtsdKLs83eoRAhHnx/T4umnUZ6/xmbI49wwXPjMfc7WDxj6pEPKFlTib3ni2rXdHk+gw7P7HHC8/K1DkYv3XJ1y9XNL1Dq3XF6ixbMZyvU+B70WdD4UMdxFdJ88FAm8N6RsIe3WQXStHk+w7X0O9n+fz7eU6PGk0fq8mPN5KRy34tkMW6w+L+58vgimaPH0Wwkt8ny+iE4UWjybtW0p6nxeWlaizWp2RfZ5cefz1LKdJfosBVs+0KLO5xsO6nZzPJshnVfxv8/nG0r3bjnObaXPP0mXF70z3C/oAAAAAElFTkSuQmCC"
          alt=""
        />
        <span data-v-c1a393be="" data-v-41d6eebc="">华尔街见闻快讯</span>
      </div>
      <div data-v-c1a393be="" data-v-41d6eebc="" class="main">
        <time
          data-v-c1a393be=""
          data-v-41d6eebc=""
          datetime="${isoTime}"
          class="created"
        >
          ${formattedTime}
        </time>
        <div data-v-c1a393be="" data-v-41d6eebc="" class="info-title">${title}</div>
        <article
          data-v-c1a393be=""
          data-v-41d6eebc=""
          class="live-detail-html content"
        >
          ${contentHtml}
        </article>
        <article
          data-v-c1a393be=""
          data-v-41d6eebc=""
          class="live-detail-html content-more"
        >
          ${contentMoreHtml}
        </article>
      </div>
    </div>
    `;
    return template;
  }
}
