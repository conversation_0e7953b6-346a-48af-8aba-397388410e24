declare module 'fastify' {
  interface FastifyInstance {
    playwright: PlaywrightService;
  }
}

import {
  chromium,
  type Browser,
  type BrowserContext,
  type Page,
} from 'playwright';

import fs from 'node:fs';
import path from 'node:path';
const __dirname = path.dirname(new URL(import.meta.url).pathname);
import { log } from '../services/log';
import { sleep } from '../services/utils';

export class PlaywrightService {
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private page: Page | null = null;
  private screenshotDir: string;

  constructor() {
    this.screenshotDir = path.join(__dirname, '../public/screenshots');
    // Create screenshots directory if it doesn't exist
    if (!fs.existsSync(this.screenshotDir)) {
      fs.mkdirSync(this.screenshotDir);
    }
  }

  async init() {
    // Launch browser in headless mode (explicitly set for clarity)
    this.browser = await chromium.launch({
      headless: true,
      // proxy: {
      //   server: 'http://5.78.99.142:8225', // Add a HTTP proxy server
      // },
      // Comprehensive args to simulate real Chrome browser and avoid detection
      args: [
        '--disable-dev-shm-usage', // Prevents OOM in limited memory environments
        '--no-sandbox', // Required in some restricted environments
        '--disable-setuid-sandbox',
        '--disable-gpu', // Reduces resource usage
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-blink-features=AutomationControlled',
        '--disable-infobars',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-images',
        '--disable-default-apps',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=TranslateUI',
        '--disable-ipc-flooding-protection',
        '--no-first-run',
        '--no-default-browser-check',
        '--no-pings',
        '--password-store=basic',
        '--use-mock-keychain',
        '--disable-component-extensions-with-background-pages',
        '--disable-background-networking',
        '--disable-sync',
        '--metrics-recording-only',
        '--disable-default-apps',
        '--mute-audio',
        '--no-zygote',
        '--disable-background-mode',
        '--disable-hang-monitor',
        '--disable-prompt-on-repost',
        '--disable-client-side-phishing-detection',
        '--disable-component-update',
        '--disable-domain-reliability',
        '--disable-features=AudioServiceOutOfProcess',
        '--disable-features=VizDisplayCompositor',
        '--disable-features=VizHitTestSurfaceLayer',
        '--disable-features=VizSurfaceDisplayCompositor',
        '--disable-crash-reporter',
        '--disable-logging',
        '--disable-dev-tools',
        '--disable-web-security',
        '--allow-running-insecure-content',
        '--ignore-certificate-errors',
        '--ignore-ssl-errors',
        '--ignore-certificate-errors-spki-list',
        '--ignore-certificate-errors-ssl-info',
        '--disable-popup-blocking',
        '--disable-notifications',
        '--disable-geolocation',
        '--lang=en-US,en;q=0.9',
      ],
    });

    // Create context with comprehensive browser simulation
    this.context = await this.browser.newContext({
      viewport: { width: 1920, height: 1080 },
      userAgent:
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      acceptDownloads: true,
      ignoreHTTPSErrors: true,
      javaScriptEnabled: true,
      locale: 'en-US',
      timezoneId: 'America/New_York',
      geolocation: { longitude: -74.006, latitude: 40.7128 },
      permissions: ['geolocation'],
      colorScheme: 'light',
      deviceScaleFactor: 1,
      hasTouch: false,
      isMobile: false,
      extraHTTPHeaders: {
        Accept:
          'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        DNT: '1',
        Connection: 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
      },
    });
    this.page = await this.context.newPage();
    return this;
  }

  async close() {
    this.browser = null;
    this.context = null;
    this.page = null;
  }

  async restart() {
    log.info('Restarting browser session...');
    await this.close();
    await this.init();
    log.info('Browser session restarted');

    // Update queue worker's browser reference if it exists
    if (global.fastify?.worker) {
      global.fastify.worker.updatePlaywright(this);
    }
  }

  async screenshot(payload: { url: string }) {
    if (!this.browser || !this.context) {
      log.error('Browser session not exist or expired.');
      await this.restart();
    }

    if (!this.context) {
      log.info('Creating new context ...');
      this.context = (await this.browser?.newContext()) ?? null;
    }

    if (!this.page || this.page.isClosed()) {
      log.info('Creating new page ...');
      this.page = (await this.context?.newPage()) ?? null;
    }

    try {
      log.info(`Navigating to ${payload.url}...`);
      if (!this.page) {
        throw new Error('Failed to create page');
      }
      await this.page.goto(payload.url, {
        waitUntil: 'domcontentloaded',
      });

      const body = await this.page.$('body');
      const bodyScreenshotName = crypto.randomUUID();
      await body?.screenshot({
        path: path.join(this.screenshotDir, `${bodyScreenshotName}.png`),
      });
      log.info('HTML body screenshot captured:', `${bodyScreenshotName}.png`);
    } catch (error) {
      log.error('Error during browsing or screenshot capture:');
      log.error(error);
      await this.restart();
    }
  }

  async getTruthSocialFeeds(payload: { url: string }) {
    if (!this.browser || !this.context) {
      log.error('Browser session not exist or expired.');
      await this.restart();
    }

    if (!this.context) {
      log.info('Creating new context ...');
      this.context = (await this.browser?.newContext()) ?? null;
    }

    if (!this.page || this.page.isClosed()) {
      log.info('Creating new page ...');
      this.page = (await this.context?.newPage()) ?? null;
    }

    try {
      log.info(`Navigating to ${payload.url}...`);
      if (!this.page) {
        throw new Error('Failed to create page');
      }

      await this.page.goto(payload.url, {
        waitUntil: 'domcontentloaded',
      });

      const preElement = await this.page.$('pre');
      if (!preElement) return null;
      const textContent = await preElement.textContent();
      if (!textContent) return null;
      return JSON.parse(textContent);
    } catch (error) {
      log.error('Error during browsing or screenshot capture:');
      log.error(error);
      await this.restart();
    }
  }

  async screenshotTruthSocial(payload: { url: string }) {
    if (!this.browser || !this.context) {
      log.error('Browser session not exist or expired.');
      await this.restart();
    }

    if (!this.context) {
      log.info('Creating new context ...');
      this.context = (await this.browser?.newContext()) ?? null;
    }

    if (!this.page || this.page.isClosed()) {
      log.info('Creating new page ...');
      this.page = (await this.context?.newPage()) ?? null;
    }

    try {
      log.info(`Navigating to ${payload.url}...`);
      if (!this.page) {
        throw new Error('Failed to create page');
      }
      await sleep(Math.random() * 3_000);
      await this.page.goto(payload.url, {
        waitUntil: 'networkidle',
        timeout: 30000, // Add timeout to prevent hanging
      });

      // thread root element
      const thread = await this.page.$('.thread');
      if (!thread) {
        log.error('TruthSocial thread not found');
        return null;
      }
      const threadScreenshotName = crypto.randomUUID();

      const statusBarElement = thread
        ? await thread.$('div[data-testid=status-action-bar]')
        : '';
      // Hide status bar element if it exists
      if (statusBarElement) {
        await this.page.evaluate((el) => {
          el.style.display = 'none';
        }, statusBarElement);
      }

      const ctaBannerElement = thread
        ? await this.page.$('div[data-testid=banner]')
        : '';
      // Hide cta banner element if it exists
      if (ctaBannerElement) {
        await this.page.evaluate((el) => {
          el.style.display = 'none';
        }, ctaBannerElement);
      }

      await thread?.screenshot({
        path: path.join(this.screenshotDir, `${threadScreenshotName}.png`),
      });
      log.info(
        'TruthSoical thread screenshot captured:',
        `${threadScreenshotName}.png`
      );

      return {
        screenshot: `${threadScreenshotName}.png`,
      };
    } catch (error) {
      log.error('Error during browsing or screenshot capture:');
      log.error(error);
      await this.restart();
    }
  }

  async getWeRssContentByURL(url: string) {
    if (!this.browser || !this.context || !this.page) {
      throw new Error('Browser session not initialized. Call init() first.');
    }
    try {
      await this.page.goto(url, {
        waitUntil: 'domcontentloaded',
      });

      const content = await this.page.$eval('#js_content', (el) => {
        // 删除所有 .video_iframe 元素
        el.querySelectorAll('.video_iframe').forEach((e) => e.remove());
        return el.textContent?.trim() || '';
      });

      const pic_url =
        (await this.page.getAttribute(
          'meta[property="og:image"]',
          'content'
        )) || '';

      return { content, pic_url };
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async createContext(): Promise<BrowserContext> {
    if (!this.browser) {
      throw new Error('Browser not initialized. Call init() first.');
    }

    return await this.browser.newContext({
      viewport: { width: 1920, height: 1080 },
      userAgent:
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      acceptDownloads: true,
      ignoreHTTPSErrors: true,
      javaScriptEnabled: true,
      locale: 'en-US',
      timezoneId: 'America/New_York',
      geolocation: { longitude: -74.006, latitude: 40.7128 },
      permissions: ['geolocation'],
      colorScheme: 'light',
      deviceScaleFactor: 1,
      hasTouch: false,
      isMobile: false,
      extraHTTPHeaders: {
        Accept:
          'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        DNT: '1',
        Connection: 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
      },
    });
  }
}
