import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, chromium, Page } from 'playwright';
import path from 'node:path';

export class Jin10 {
  private static browser: Browser;
  private static context: BrowserContext;
  private static page: Page;
  private static screenshotDir: string;

  public static async init() {
    this.screenshotDir = 'public/screenshots';
    this.browser = await chromium.launch();
    this.context = await this.browser.newContext({
      viewport: { width: 1920, height: 1080 },
      deviceScaleFactor: 2,
    });
    this.page = await this.context.newPage();
    this.page.goto('https://www.jin10.com/', {
      waitUntil: 'domcontentloaded',
    });
  }

  public static async screenshot(time: string, content: string) {
    const uuid = crypto.randomUUID();
    const id = `id-${uuid}`;
    const insertHTML = this.getHTML(id, time, content);
    await this.page.evaluate((html) => {
      const div = document.createElement('div');
      div.innerHTML = html;
      document.body.appendChild(div);
    }, insertHTML);

    await this.page.waitForSelector(`#${id}`);
    await this.page.waitForTimeout(500);
    const element = this.page.locator(`#${id}`);
    await element.screenshot({
      path: `${this.screenshotDir}/${uuid}.png`,
    });

    await this.page.evaluate((id) => {
      document.querySelectorAll(`#${id}`).forEach((el) => el.remove());
    }, id);

    return uuid;
  }

  public static formatTime(time: string) {
    const dt = new Date(time.replace(' ', 'T'));

    const month = String(dt.getMonth() + 1).padStart(2, '0');
    const day = String(dt.getDate()).padStart(2, '0');
    const hour = String(dt.getHours()).padStart(2, '0');
    const minute = String(dt.getMinutes()).padStart(2, '0');
    const second = String(dt.getSeconds()).padStart(2, '0');

    const weekdays = [
      '星期日',
      '星期一',
      '星期二',
      '星期三',
      '星期四',
      '星期五',
      '星期六',
    ];
    const weekday = weekdays[dt.getDay()];

    return `${month}月${day}日 ${hour}:${minute}:${second} ${weekday}`;
  }

  public static getHTML(id: string, time: string, text: string) {
    const formattedTime = this.formatTime(time);
    const { title, content } = this.getTitleAndContent(text);
    const template = `<div id="${id}"
  class="share-dialog van-popup van-popup--center"
  style="width: 360px; z-index: 2002;"
>
  <div class="share-popup">
    <div class="dom-screeshot-container" style="background-color: white;">
      <div class="dom-screenshot">
        <div class="flash-preview" style="margin: 0px">
          <div class="preview-content">
            <div class="flash-time">${formattedTime}</div>
            ${title && `<div class="flash-title is-important">${title}</div>`}
            <div class="flash-content is-important">${content}</div>
          </div>
          <div class="preview-foot">
            <div class="preview-foot_left" style="height: 304px;">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

    return template;
  }
  public static getTitleAndContent(content: string) {
    const match = content.match(/【(.+?)】(.*)$/);

    if (match) {
      return {
        title: match[1],
        content: match[2].trim(),
      };
    }

    return {
      title: '',
      content: content,
    };
  }
}
