import { load } from 'cheerio';
import type { FastifyInstance } from 'fastify';
import fs from 'fs/promises';
import path from 'node:path';

import {
    bloombergResponseSchema,
    failedResponseSchema,
    successfulResponseSchema,
} from '../../../schemas';
import { Bloomberg } from '../../../services/bloomberg';

export async function bloombergRoutes(fastify: FastifyInstance): Promise<void> {
  fastify.get(
    '/bloomberg',
    {
      schema: {
        response: {
          '2xx': successfulResponseSchema.extend({
            data: bloombergResponseSchema,
          }),
          '5xx': failedResponseSchema,
        },
      },
    },

    async (req, res) => {
      try {
        const data = await getBloombergNews(fastify);
        res.status(200).send({
          err: false,
          msg: 'ok',
          data,
        });
      } catch (error) {
        res.status(500).send({
          err: true,
          msg: error.message,
          data: null,
        });
      }
    }
  );
}

function cleanContent(input: string): string {
  try {
    const isHtml = /<\/?[a-z][\s\S]*>/i.test(input);
    if (!isHtml) return input;

    const $ = load(input);
    return $.text().trim();
  } catch (error) {
    return '';
  }
}

function parseTimeAgo(timeText: string): number {
  const now = Date.now();
  const timeStr = timeText.toLowerCase().trim();
  
  if (timeStr.includes('min ago')) {
    const minutes = parseInt(timeStr.match(/(\d+)\s*min/)?.[1] || '0');
    return now - (minutes * 60 * 1000);
  } else if (timeStr.includes('hr ago') || timeStr.includes('hour ago')) {
    const hours = parseInt(timeStr.match(/(\d+)\s*hr/)?.[1] || '0');
    return now - (hours * 60 * 60 * 1000);
  } else if (timeStr.includes('day ago')) {
    const days = parseInt(timeStr.match(/(\d+)\s*day/)?.[1] || '0');
    return now - (days * 24 * 60 * 60 * 1000);
  }
  
  // Default to current time if can't parse
  return now;
}

async function getBloombergNews(fastify: FastifyInstance) {
  const url = 'https://www.bloomberg.com/latest';

  // Use Playwright to fetch the page content to avoid 403 errors
  const context = await fastify.playwright.createContext();
  const page = await context.newPage();

  let newsItems: any[] = [];

  try {
    // Navigate to the page
    await page.goto(url, {
      waitUntil: 'domcontentloaded',
      timeout: 30000,
    });

    // Wait for content to load
    await page.waitForTimeout(3000);

    // Get the HTML content
    const html = await page.content();

    // Close the page and context
    await page.close();
    await context.close();

    const $ = load(html);

    // Find news items in the latest news section
    // Look for containers that have both time and link elements
    $('time').each((index, timeElement) => {
      const $timeElement = $(timeElement);
      const timeText = $timeElement.text().trim();
      if (!timeText) return; // Skip if no time found

      // Find the parent container that contains both time and link
      const $container = $timeElement.closest('div').parent();

      // Look for links to news articles in this container
      const $titleLink = $container.find('a[href*="/news/articles/"], a[href*="/opinion/articles/"]').first();
      if (!$titleLink.length) return;

      const relativeUrl = $titleLink.attr('href');
      if (!relativeUrl) return;

      // Extract title from the link
      let title = '';
      const titleElement = $titleLink.find('div, span').last();
      if (titleElement.length) {
        title = titleElement.text().trim();
      } else {
        title = $titleLink.text().trim();
      }

      if (!title) return;

      const fullUrl = relativeUrl.startsWith('http') ? relativeUrl : `https://www.bloomberg.com${relativeUrl}`;

      // Extract article ID from URL
      const urlMatch = relativeUrl.match(/\/(news|opinion)\/articles\/(\d{4}-\d{2}-\d{2})\/(.+)/);
      if (!urlMatch) return;

      const [, type, date, slug] = urlMatch;
      const id = `${date}-${slug}`;

      // Parse timestamp
      const timestamp = parseTimeAgo(timeText);

      // Extract summary/description if available
      let summary = '';
      const summaryElements = $container.find('div').filter((i, el) => {
        const text = $(el).text().trim();
        return text.length > 50 && text !== title;
      });
      if (summaryElements.length > 0) {
        summary = summaryElements.first().text().trim();
      }

      newsItems.push({
        id,
        timestamp,
        title: cleanContent(title),
        content: cleanContent(summary || title),
        url: fullUrl,
        timeText,
        type, // news or opinion
      });
    });

  } catch (error) {
    await page.close();
    await context.close();
    throw new Error(`Failed to fetch Bloomberg news: ${error.message}`);
  }

  // Remove duplicates and sort by timestamp (newest first)
  const uniqueNews = newsItems
    .filter((item, index, self) => 
      index === self.findIndex(t => t.id === item.id)
    )
    .sort((a, b) => b.timestamp - a.timestamp)
    .slice(0, 20); // Limit to 20 most recent items

  // Generate screenshots for each news item
  const results: any[] = [];
  for (const newsItem of uniqueNews) {
    try {
      const screenshotId = crypto.randomUUID();
      await Bloomberg.screenshot({ id: screenshotId, uri: newsItem.url });

      // Upload to S3 bucket
      await fastify.s3.uploadObject(
        `screenshot/bloomberg/${screenshotId}.png`,
        path.join('./public/screenshots', `${screenshotId}.png`)
      );
      
      // Remove local files
      await fs.unlink(path.join('./public/screenshots', `${screenshotId}.png`));

      const screenshot = `https://${process.env.S3_BUCKET}.${process.env.S3_ENDPOINT}/screenshot/bloomberg/${screenshotId}.png`;
      
      results.push({
        id: newsItem.id,
        timestamp: newsItem.timestamp,
        title: newsItem.title,
        content: newsItem.content,
        screenshot,
      });
    } catch (error) {
      console.error(`Failed to process news item ${newsItem.id}:`, error);
      // Continue with other items even if one fails
    }
  }

  return results;
}
