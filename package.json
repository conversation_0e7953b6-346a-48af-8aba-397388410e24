{"name": "x-crawler", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"dev": "tsx watch --env-file=.env main.ts", "dev:mirrord": "mirrord exec -f .mirrord/mirrord.json -- tsx watch --env-file=.env main.ts", "test": "vitest --testTimeout 120000 ./tests", "test:x": "vitest --testTimeout 120000 ./tests/tweet.test.ts", "test:ts": "vitest --testTimeout 120000 ./tests/truthsocial.test.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@dotenvx/dotenvx": "^1.44.1", "@fastify/cors": "^11.0.1", "@fastify/static": "^8.2.0", "bullmq": "^5.53.2", "cheerio": "^1.0.0", "fastify": "^5.3.3", "fastify-healthcheck": "^5.1.0", "fastify-metrics": "^12.1.0", "fastify-plugin": "^5.0.1", "fastify-type-provider-zod": "^4.0.2", "ioredis": "^5.6.1", "minio": "^8.0.5", "node-schedule": "^2.1.1", "pino-pretty": "^13.0.0", "playwright": "^1.52.0", "steel-sdk": "^0.5.0", "tsx": "^4.19.4", "vitest": "^3.2.4", "zod": "^3.25.30"}, "devDependencies": {"@types/node": "^22.15.21", "typescript": "^5.8.3"}}