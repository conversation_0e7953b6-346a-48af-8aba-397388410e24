FROM node:22-slim

WORKDIR /app

COPY package.json pnpm-lock.yaml* ./

RUN npm install -g pnpm tsx && \
    pnpm install --frozen-lockfile

COPY . .

RUN npx playwright install --with-deps chromium && \
    mkdir -p /app/public/screenshots

# run the app
EXPOSE 8123/tcp
CMD [ "tsx", "main.ts" ]

# build & run
# sudo docker build -t x-crawler-api:20250527 .
# sudo docker run -p 8123:8123 --env-file=.env.local x-crawler-api:20250527
