import type { FastifyInstance } from 'fastify';
import fs from 'fs/promises';
import path from 'node:path';

import {
  failedResponseSchema,
  successfulResponseSchema,
  wallStreetResponseSchema,
} from '../../../schemas';
import { Wallstreet } from '../../../services/wallstreet';

export async function wallStreetRoutes(
  fastify: FastifyInstance
): Promise<void> {
  fastify.get(
    '/wallStreet',
    {
      schema: {
        response: {
          '2xx': successfulResponseSchema.extend({
            data: wallStreetResponseSchema,
          }),
          '5xx': failedResponseSchema,
        },
      },
    },

    async (req, res) => {
      try {
        const data = await getData();
        res.status(200).send({
          err: false,
          msg: 'ok',
          data,
        });
      } catch (error) {
        global.fastify.log.error(error);
        res.status(500).send({
          err: true,
          msg: error.message,
          data: null,
        });
      }
    }
  );
}

const getData = async () => {
  const url =
    'https://api-one-wscn.awtmt.com/apiv1/search/live?channel=global-channel&limit=20&score=2';
  const response = await fetch(url);
  const result = await response.json();
  const news = result.data.items;
  // const news = ITEM;

  const data = news.map((n) => ({
    id: n.id.toString(),
    timestamp: n.display_time * 1000,
    title: n.title,
    content: n.content_text,
    contentHtml: n.content,
    contentMoreHtml: n.content_more,
    uri: n.uri,
  }));

  const results = await Promise.all(
    data.map(async (d) => {
      const screenshotId = d.id;
      const screenshot = `https://${process.env.S3_BUCKET}.${process.env.S3_ENDPOINT}/screenshot/wallstreet/${screenshotId}.png`;

      if (!global.fastify.redis.get(screenshotId)) {
        await Wallstreet.screenshot({ id: d.id, uri: d.uri });
        // upload to s3 bucket
        await global.fastify.s3.uploadObject(
          `screenshot/wallstreet/${screenshotId}.png`,
          path.join('./public/screenshots', `${screenshotId}.png`)
        );
        // remove local files
        await fs.unlink(
          path.join('./public/screenshots', `${screenshotId}.png`)
        );

        global.fastify.redis.set(screenshotId, screenshot);
      }

      return {
        ...d,
        screenshot,
      };
    })
  );
  console.timeEnd('all');
  return results;
};
