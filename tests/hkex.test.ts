import { assert, expect, test } from 'vitest';

const host =
  process.env.NODE_ENV === 'development'
    ? 'http://127.0.0.1:8123'
    : 'https://defomo-x-crawler-api.dev.cn.defomo.hony.love';

test('request to HKEX news route', async () => {
  const response = await fetch(`${host}/api/v1/hkex/news`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
  });

  expect(response.status).toBe(200);
  
  const result = await response.json();
  console.log('HKEX news result:', result);
  expect(result.err).toBe(false);
  expect(result.data).toBeDefined();
  expect(result.data.news).toBeDefined();
  expect(Array.isArray(result.data.news)).toBe(true);
}, 120000);

test('request to HKEX news route with screenshot', async () => {
  const response = await fetch(`${host}/api/v1/hkex/news?screenshot=true`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
  });

  expect(response.status).toBe(200);
  
  const result = await response.json();
  console.log('HKEX news with screenshot result:', result);
  expect(result.err).toBe(false);
  expect(result.data).toBeDefined();
  expect(result.data.news).toBeDefined();
  expect(result.data.screenshot).toBeDefined();
}, 120000);

test('async request to HKEX news route', async () => {
  const response = await fetch(`${host}/api/v1/hkex/news/async`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    body: JSON.stringify({
      screenshot: false,
    }),
  });

  expect(response.status).toBe(200);
  
  const result = await response.json();
  console.log('HKEX async request result:', result);
  expect(result.err).toBe(false);
  expect(result.data).toBeDefined();
  expect(result.data.id).toBeDefined();
});
