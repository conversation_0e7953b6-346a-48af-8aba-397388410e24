import type { FastifyInstance } from 'fastify';
import type z from 'zod';
import path from 'node:path';

import {
  tweetScreenshotPayloadSchema,
  tweetScreenshotResponseSchema,
  successfulResponseSchema,
  badRequestResponseSchema,
  failedResponseSchema,
} from '../../../schemas';

// Declare routes
export async function tweetRoutes(fastify: FastifyInstance): Promise<void> {
  fastify.post(
    '/tweet/screenshot/generate',
    {
      schema: {
        body: tweetScreenshotPayloadSchema,
        response: {
          '2xx': successfulResponseSchema.extend({
            data: tweetScreenshotResponseSchema,
          }),
          '4xx': badRequestResponseSchema,
          '5xx': failedResponseSchema,
        },
      },
    },
    async (req, res) => {
      try {
        const { url } = req.body as z.infer<
          typeof tweetScreenshotPayloadSchema
        >;

        if (!url) {
          res.status(400).send({
            err: true,
            msg: 'Invalid URL',
            data: null,
          });
          return;
        }

        // add a job to the queue
        try {
          const result = await fastify.queue.addAndWait(
            'screenshotTwitter',
            {
              url,
              channel: 'twitter',
              requestedAt: new Date().toISOString(),
            },
            120_000
          );

          const tweetResp = result.result?.screenshot;

          if (!tweetResp) {
            res.status(400).send({
              err: true,
              msg: 'Fetch tweet failed',
              data: null,
            });
            return;
          }

          // upload to s3 bucket
          await fastify.s3.uploadObject(
            `screenshot/x/${tweetResp?.screenshot}`,
            path.join('./public/screenshots', `${tweetResp?.screenshot}`)
          );
          tweetResp.screenshot = `https://${process.env.S3_BUCKET}.${process.env.S3_ENDPOINT}/screenshot/x/${tweetResp?.screenshot}`;

          res.status(200).send({
            err: false,
            msg: 'Screenshot generated successfully',
            data: result.result?.screenshot,
          });
        } catch (error) {
          fastify.log.error('Queue job error:');
          fastify.log.error(error);
          res.status(500).send({
            err: true,
            msg: error.message || 'Failed to process screenshot request',
            data: null,
          });
        }
      } catch (error) {
        fastify.log.error(error);
        res.status(500).send({
          err: false,
          msg: error.message,
          data: null,
        });
      }
    }
  );
}
