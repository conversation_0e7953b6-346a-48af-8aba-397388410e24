declare module 'fastify' {
  interface FastifyInstance {
    s3: S3Service;
  }
}

import { Client as MinioClient } from 'minio';
import type { UploadedObjectInfo } from 'minio/dist/esm/internal/type.mjs';

export class S3Service {
  private client: MinioClient;
  private bucket: string;
  private endPoint: string;
  private accessKey: string;
  private secretKey: string;
  private region?: string;

  constructor({
    endPoint = process.env.S3_ENDPOINT,
    region = process.env.S3_ENDPOINT?.split('.').at(0),
    accessKey = process.env.S3_ACCESS_KEY,
    secretKey = process.env.S3_SECRET_KEY,
    bucket,
  }: {
    endPoint?: string;
    region?: string;
    port?: number;
    useSSL?: boolean;
    accessKey?: string;
    secretKey?: string;
    bucket: string;
  }) {
    if (!endPoint) throw new Error('S3_ENDPOINT is required');
    if (!accessKey) throw new Error('S3_ACCESS_KEY is required');
    if (!secretKey) throw new Error('S3_SECRET_KEY is required');
    if (!bucket) throw new Error('bucket is required');
    this.endPoint = endPoint;
    this.accessKey = accessKey;
    this.secretKey = secretKey;
    this.region = region;
    this.bucket = bucket;
  }

  async init() {
    this.client = new MinioClient({
      endPoint: this.endPoint,
      accessKey: this.accessKey,
      secretKey: this.secretKey,
      region: this.region,
      pathStyle: false,
    });
    return this;
  }

  async uploadObject(
    objectName: string,
    filePath: string,
    metaData = {}
  ): Promise<UploadedObjectInfo> {
    return await this.client.fPutObject(
      this.bucket,
      objectName,
      filePath,
      metaData
    );
  }

  async getObject(objectName: string): Promise<NodeJS.ReadableStream> {
    return this.client.getObject(this.bucket, objectName);
  }

  async listObjects(prefix = ''): Promise<string[]> {
    return new Promise((resolve, reject) => {
      const objects: string[] = [];
      const stream = this.client.listObjectsV2(this.bucket, prefix || '', true);
      stream.on('data', (obj) => objects.push(obj.name ?? ''));
      stream.on('end', () => resolve(objects.filter(Boolean)));
      stream.on('error', reject);
    });
  }
}
