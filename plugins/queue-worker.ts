import fastifyPlugin from 'fastify-plugin';
import { QueueWorker } from '../services/queue-worker';
import { queueRedis } from '../services/queue-redis';

async function QueueWorkerService(fastify, options) {
  const worker = new QueueWorker(
    fastify.browser,
    fastify.playwright,
    queueRedis
  );

  fastify.log.info('Queue worker service initialized');
  fastify.decorate('worker', worker);
}

export default fastifyPlugin(QueueWorkerService);
