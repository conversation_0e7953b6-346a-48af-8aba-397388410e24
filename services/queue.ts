declare module 'fastify' {
  interface FastifyInstance {
    queue: RequestQueue;
  }
}

import type { Redis } from 'ioredis';
import { Queue, QueueEvents } from 'bullmq';

export class RequestQueue {
  private queue: Queue;
  private queueEvents: QueueEvents;
  private connection: Redis;
  redis: Redis;

  constructor(redis: Redis) {
    this.connection = redis;

    this.queue = new Queue(process.env.QUEUE_NAME || 'request-queue', {
      connection: this.connection,
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 1000,
        },
        removeOnComplete: 100,
        removeOnFail: 1000,
      },
    });

    // Create QueueEvents for listening to job completion
    this.queueEvents = new QueueEvents(
      process.env.QUEUE_NAME || 'request-queue',
      {
        connection: this.connection.duplicate(), // Use a separate connection for events
      }
    );
  }

  async add(
    name: string,
    data: Record<string, unknown>,
    timeoutMs = 30000
  ): Promise<{ id: string; result: unknown }> {
    const job = await this.queue.add(name, data);

    if (!job.id) {
      throw new Error('Job was created without an ID');
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.queueEvents.removeAllListeners(`completed:${job.id}`);
        this.queueEvents.removeAllListeners(`failed:${job.id}`);
        reject(new Error(`Job processing timeout after ${timeoutMs}ms`));
      }, timeoutMs);

      // Listen for job completion
      this.queueEvents.once(`completed:${job.id}`, (result) => {
        clearTimeout(timeout);
        resolve({ id: job.id!, result: result.returnvalue });
      });

      // Listen for job failure
      this.queueEvents.once(`failed:${job.id}`, (error) => {
        clearTimeout(timeout);
        reject(new Error(`Job failed: ${error.failedReason}`));
      });
    });
  }

  // Alternative method using waitUntilFinished (recommended)
  async addAndWait(
    name: string,
    data: Record<string, unknown>,
    timeoutMs = 30000
  ): Promise<{ id: string; result: unknown }> {
    const job = await this.queue.add(name, data);

    if (!job.id) {
      throw new Error('Job was created without an ID');
    }

    try {
      const result = await job.waitUntilFinished(this.queueEvents, timeoutMs);
      return { id: job.id, result };
    } catch (error) {
      throw new Error(
        `Job failed or timed out: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  }

  // Method to get job status manually
  async getJobStatus(
    jobId: string
  ): Promise<{ id: string; status: string; result?: unknown; error?: string }> {
    const job = await this.queue.getJob(jobId);

    if (!job) {
      throw new Error('Job not found');
    }

    const state = await job.getState();

    return {
      id: jobId,
      status: state,
      result: job.returnvalue || undefined,
      error: job.failedReason || undefined,
    };
  }

  // Method for fire-and-forget (don't wait for result)
  async addJob(
    name: string,
    data: Record<string, unknown>
  ): Promise<{ id: string }> {
    const job = await this.queue.add(name, data);
    return { id: job.id! };
  }

  async close() {
    await this.queueEvents.close();
    await this.queue.close();
    await this.connection.quit();
  }
}