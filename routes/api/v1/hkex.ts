import type { FastifyInstance } from 'fastify';
import { z } from 'zod';
import { 
  successfulResponseSchema,
  failedResponseSchema,
  badRequestResponseSchema,
  hkexPayloadSchema,
  hkexResponseSchema,
  type HKEXPayload,
} from '../../../schemas';

export async function hkexRoutes(fastify: FastifyInstance): Promise<void> {
  // Get HKEX news releases
  fastify.get(
    '/hkex/news',
    {
      schema: {
        querystring: hkexPayloadSchema,
        response: {
          '2xx': successfulResponseSchema.extend({
            data: hkexResponseSchema,
          }),
          '4xx': badRequestResponseSchema,
          '5xx': failedResponseSchema,
        },
      },
    },
    async (request, reply) => {
      try {
        const query = request.query as HKEXPayload;
        
        // Add HKEX scraping job to the queue
        const jobData = {
          channel: 'hkex',
          screenshot: query.screenshot || false,
        };

        // Use the queue to process the request
        const result = await fastify.queue.addAndWait(
          'hkex-scraper',
          jobData,
          60000 // 60 seconds timeout
        );

        return reply.status(200).send({
          err: false,
          msg: 'HKEX news scraped successfully',
          data: result.result,
        });
      } catch (error) {
        fastify.log.error('Error processing HKEX news request:', error);
        return reply.status(500).send({
          err: true,
          msg: error.message || 'Failed to scrape HKEX news',
          data: null,
        });
      }
    }
  );

  // Get HKEX news releases (fire-and-forget)
  fastify.post(
    '/hkex/news/async',
    {
      schema: {
        body: hkexPayloadSchema,
        response: {
          '2xx': successfulResponseSchema.extend({
            data: z.object({
              id: z.string(),
            }),
          }),
          '4xx': badRequestResponseSchema,
          '5xx': failedResponseSchema,
        },
      },
    },
    async (request, reply) => {
      try {
        const body = request.body as HKEXPayload;
        
        // Add HKEX scraping job to the queue (fire-and-forget)
        const jobData = {
          channel: 'hkex',
          screenshot: body.screenshot || false,
        };

        const result = await fastify.queue.addJob(
          'hkex-scraper',
          jobData
        );

        return reply.status(200).send({
          err: false,
          msg: 'HKEX scraping job queued successfully',
          data: result,
        });
      } catch (error) {
        fastify.log.error('Error queuing HKEX news request:', error);
        return reply.status(500).send({
          err: true,
          msg: error.message || 'Failed to queue HKEX scraping job',
          data: null,
        });
      }
    }
  );

  // Get job status
  fastify.get(
    '/hkex/jobs/:jobId',
    {
      schema: {
        params: z.object({
          jobId: z.string(),
        }),
        response: {
          '2xx': successfulResponseSchema.extend({
            data: z.object({
              id: z.string(),
              status: z.string(),
              result: z.object({}).optional().nullable(),
              error: z.string().optional().nullable(),
            }),
          }),
          '4xx': badRequestResponseSchema,
          '5xx': failedResponseSchema,
        },
      },
    },
    async (request, reply) => {
      try {
        const { jobId } = request.params as { jobId: string };
        
        const jobStatus = await fastify.queue.getJobStatus(jobId);
        
        return reply.status(200).send({
          err: false,
          msg: 'Job status retrieved successfully',
          data: jobStatus,
        });
      } catch (error) {
        fastify.log.error('Error getting job status:', error);
        return reply.status(500).send({
          err: true,
          msg: error.message || 'Failed to get job status',
          data: null,
        });
      }
    }
  );
}
