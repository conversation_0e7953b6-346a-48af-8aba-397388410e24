# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在处理此仓库代码时提供指导。

## 项目概述

X-Crawler API 是一个基于 Fastify 和 TypeScript 构建的高性能网页爬虫 API 服务。它提供了从各种金融和社交媒体源（包括香港交易所、金十数据、Twitter/X、Truth Social 和华尔街数据）提取数据和截图的接口。

## 开发命令

### 运行应用
```bash
# 本地开发（热重载）
npm run dev

# 使用 Mirrord 远程开发
npm run dev:mirrord

# 生产环境运行（直接使用 tsx）
tsx main.ts
```

### 测试
```bash
# 运行所有测试
npm test

# 运行特定测试套件
npm run test:x      # Twitter 测试
npm run test:ts     # Truth Social 测试
```

### Docker 操作
```bash
# 构建镜像
docker build -t x-crawler-api:20250527 .

# 运行容器
docker run -p 8123:8123 --env-file=.env.local x-crawler-api:20250527

# 使用 docker-compose
docker-compose up
```

## 架构概览

### 核心技术
- **运行时**: Node.js v22 配合 TypeScript (ES modules)
- **框架**: Fastify v5 插件架构
- **类型验证**: Zod 运行时类型检查
- **浏览器自动化**: Playwright 和 Steel SDK
- **队列系统**: BullMQ 配合 Redis 后端
- **存储**: MinIO (兼容 S3)
- **测试**: Vitest

### 服务架构

应用采用基于插件的架构，服务注册为 Fastify 插件：

1. **插件层** (`/plugins/`): 服务初始化和依赖注入
   - `browser-service.ts`: Steel 浏览器自动化
   - `playwright-service.ts`: Playwright 浏览器自动化
   - `queue.ts`: BullMQ 任务队列管理
   - `redis-service.ts`: Redis 连接管理
   - `s3-service.ts`: MinIO/S3 对象存储

2. **路由层** (`/routes/api/v1/`): API 端点定义
   - 每个路由文件对应一个数据源
   - 路由使用 Zod 模式进行请求/响应验证
   - 支持同步和异步（基于队列）操作

3. **服务层** (`/services/`): 业务逻辑实现
   - 每个服务处理特定数据源的爬虫逻辑
   - 服务通常是由路由处理器管理的单例
   - 常见模式：使用 `getInstance()` 访问单例

4. **模式层** (`/schemas/`): Zod 模式定义
   - 集中的请求/响应模式
   - 共享验证逻辑

### 队列架构

应用支持两种长时间运行操作的模式：
- **同步模式** (`wait=true`): 客户端等待操作完成
- **异步模式** (`wait=false`): 返回任务 ID 供状态轮询

队列命名规范：`{service}-queue`（例如：`tweet-queue`、`truthsocial-queue`）

### API 端点

基础 URL: `http://localhost:8123`

主要端点：
- `GET /health` - 健康检查
- `GET /metrics` - Prometheus 指标
- `GET /api/v1/hkex/news` - 香港交易所新闻数据
- `GET /api/v1/jin10` - 金十财经数据
- `POST /api/v1/tweet/screenshot` - Twitter/X 截图
- `POST /api/v1/truthsocial` - Truth Social 内容
- `GET /api/v1/wallstreet` - 华尔街数据

### 环境配置

必需的环境变量：
- `REDIS_URL` - Redis 连接字符串
- `MINIO_*` - MinIO 配置（端点、访问密钥、密钥）
- `STEEL_API_KEY` - Steel 浏览器服务 API 密钥
- `PORT` - 服务器端口（默认：8123）

### 关键模式和约定

1. **错误处理**: 使用 Fastify 错误处理器，返回合适的 HTTP 状态码
2. **日志记录**: 使用 Pino（Fastify 默认日志器）进行结构化日志记录
3. **类型安全**: 所有 API 输入/输出使用 Zod 模式验证
4. **服务注册**: 服务注册为 Fastify 装饰器
5. **文件命名**: 使用 kebab-case，与服务/路由名称匹配

### 测试方法

- 测试位于 `/tests/` 目录
- 使用 Vitest，浏览器测试延长超时时间（120秒）
- 测试文件遵循 `{service}.test.ts` 命名规范
- 测试可以访问真实服务（集成测试方法）

### 性能考虑

- 选择 Fastify 以获得高吞吐量性能
- Redis 和浏览器实例的连接池
- 队列系统防止对目标站点造成过大压力
- 截图缓存减少重复操作

### 开发提示

- 使用 `npm run dev` 进行热重载开发
- 检查 `/health` 端点验证服务状态
- 通过 BullMQ 仪表板监控队列任务（如果已配置）
- 日志是结构化 JSON，使用 `pino-pretty` 获得可读输出
- 添加新爬虫时，遵循现有的 service/route/schema 模式