import { fileURLToPath } from 'node:url';
import { dirname, resolve } from 'node:path';
import process from 'node:process';
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
import Fastify from 'fastify';

// Initialize with a logger
const server = Fastify({
  logger: {
    level: 'info',
    transport: {
      target: 'pino-pretty',
    },
  },
});
const log = server.log;

// Make Fastify instance available globally
declare global {
  var fastify: typeof server;
}
global.fastify = server;

// CORS for APIs
import cors from '@fastify/cors';
await server.register(cors);

// Static files
import fastifyStatic from '@fastify/static';
server.register(fastifyStatic, {
  root: resolve(__dirname, 'public'),
  prefix: '/public/', // optional: default '/'
});

// Initialize steel services
import browserService from './plugins/browser-service';
await server.register(browserService);

// Initialize playwright services
import playwrightService from './plugins/playwright-service';
await server.register(playwrightService);

// Initialize s3 service
import s3Service from './plugins/s3-service';
await server.register(s3Service);

// Initialize redis service
import redisService from './plugins/redis-service';
await server.register(redisService);

// Initialize queue service
import RequestQueueService from './plugins/queue';
await server.register(RequestQueueService);

// Initialize queue worker service
import QueueWorkerService from './plugins/queue-worker';
await server.register(QueueWorkerService);

// Initialize cron service
import CronService from './plugins/cron-service';
await server.register(CronService);

import { Jin10 } from './services/jin10';
await Jin10.init();
await Wallstreet.init();

// Import types
import {
  serializerCompiler,
  validatorCompiler,
  type ZodTypeProvider,
} from 'fastify-type-provider-zod';
server.withTypeProvider<ZodTypeProvider>();

// Add schema validator and serializer
server.setValidatorCompiler(validatorCompiler);
server.setSerializerCompiler(serializerCompiler);

// health check
import healthcheckPlugin from 'fastify-healthcheck';
server.register(healthcheckPlugin, {
  healthcheckUrl: '/health',
  exposeUptime: true, // process uptime
  // healthcheckUrlAlwaysFail: true, // for testing failure responses
  // underPressureOptions: { // options for under-pressure
  //     maxConcurrentRequests: 100,
  //     maxRequestsPerSecond: 1
  // }
});

// prometheus metrics
import metricsPlugin from 'fastify-metrics';
await server.register(metricsPlugin, {
  endpoint: '/metrics',
  routeMetrics: {
    overrides: {
      histogram: {
        name: 'pm_server_http_request_duration_seconds',
        buckets: [0.1, 0.5, 1, 3, 5],
      },
      summary: {
        name: 'pm_server_http_request_summary_seconds',
        labelNames: ['status_code', 'method', 'route'],
        percentiles: [0.5, 0.75, 0.9, 0.95, 0.99],
      },
    },
  },
});

// Import and Register routes
import {
  tweetRoutes,
  jin10Routes,
  truthSocialRoutes,
  werssRoutes,
  wallStreetRoutes,
  hkexRoutes,
} from './routes/api/v1';
import { Wallstreet } from './services/wallstreet';
server.register(tweetRoutes, { prefix: '/api/v1' });
server.register(jin10Routes, { prefix: '/api/v1' });
server.register(truthSocialRoutes, { prefix: '/api/v1' });
server.register(werssRoutes, { prefix: '/api/v1' });
server.register(wallStreetRoutes, { prefix: '/api/v1' });
server.register(hkexRoutes, { prefix: '/api/v1' });

// Server listen
try {
  log.info('Starting server...');
  const addr = await server.listen({
    host: '0.0.0.0',
    port: 8123,
  });
  log.info(`Server listening at: ${addr}`);
  // log.info(process.env)
} catch (error) {
  log.error('Server (:8123) Uncaught Error:');
  log.error(error);
  process.exit(1);
}
