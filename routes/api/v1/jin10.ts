import { load } from 'cheerio';
import type { FastifyInstance } from 'fastify';
import fs from 'fs/promises';
import path from 'node:path';

import {
  failedResponseSchema,
  jin10ResponseSchema,
  successfulResponseSchema,
} from '../../../schemas';
import { Jin10 } from '../../../services/jin10';

const newsTypeMap = {
  crypto: '19',
  tech: '22',
};

export async function jin10Routes(fastify: FastifyInstance): Promise<void> {
  fastify.get(
    '/jin10',
    {
      schema: {
        response: {
          '2xx': successfulResponseSchema.extend({
            data: jin10ResponseSchema,
          }),
          '5xx': failedResponseSchema,
        },
      },
    },

    async (req, res) => {
      try {
        const cryptoData = await getNews('crypto', fastify);
        const techData = await getNews('tech', fastify);
        const data = [...cryptoData, ...techData];
        res.status(200).send({
          err: false,
          msg: 'ok',
          data,
        });
      } catch (error) {
        res.status(500).send({
          err: false,
          msg: error.message,
          data: null,
        });
      }
    }
  );
}

function cleanContent(input: string): string {
  try {
    const isHtml = /<\/?[a-z][\s\S]*>/i.test(input);
    if (!isHtml) return input;

    const $ = load(input);
    return $.text().trim();
  } catch (error) {
    return '';
  }
}

async function getNews(type: string, fastify: FastifyInstance) {
  const classify = newsTypeMap[type];
  const url = `https://4a735ea38f8146198dc205d2e2d1bd28.z3c.jin10.com/flash?channel=-8200&vip=1&classify=[${classify}]`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'x-app-id': 'bVBF4FyRTn5NJF5n',
      'x-version': '1.0',
      'Content-Type': 'application/json',
    },
  });
  const result = await response.json();
  const news = result.data;

  const newsIds = news.map((n) => n.id);
  const hotNewsResult = await fetch(
    'https://********************************.z3c.jin10.com/flash/hot',
    {
      method: 'POST',
      headers: {
        'x-app-id': 'bVBF4FyRTn5NJF5n',
        'x-version': '1.0',
        'Content-Type': 'application/json',
        'Accept-Encoding': 'gzip, deflate, br',
      },
      body: JSON.stringify({ ids: newsIds }),
    }
  );
  const hotNewsData = await hotNewsResult.json();

  let data = news
    .filter((n) => {
      const hot = hotNewsData.data.find((news) => news.id === n.id)?.hot ?? '';
      return (
        n.important === 1 &&
        n.type === 0 &&
        hot === '爆' &&
        !n.data.title.includes('金十')
      );
    })
    .map((n) => {
      return {
        id: n.id,
        time: n.time,
        title: n.data.title ?? '',
        hot: '爆',
        type,
        content: cleanContent(n.data.content),
      };
    });

  const results: any = [];
  for (const n of data) {
    const screenshotId = await Jin10.screenshot(n.time, n.content);

    // upload to s3 bucket
    await fastify.s3.uploadObject(
      `screenshot/jin10/${screenshotId}.png`,
      path.join('./public/screenshots', `${screenshotId}.png`)
    );
    // remove local files
    await fs.unlink(path.join('./public/screenshots', `${screenshotId}.png`));

    const screenshot = `https://${process.env.S3_BUCKET}.${process.env.S3_ENDPOINT}/screenshot/jin10/${screenshotId}.png`;
    results.push({
      ...n,
      screenshot,
    });
  }
  data = results;

  return data;
}
