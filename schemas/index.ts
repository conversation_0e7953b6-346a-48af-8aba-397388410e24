import { z } from 'zod';

// Common response schemas
export const successfulResponseSchema = z.object({
  err: z.boolean(),
  msg: z.string(),
});

export const badRequestResponseSchema = z.object({
  err: z.boolean(),
  msg: z.string(),
  data: z.null(),
});

export const failedResponseSchema = z.object({
  err: z.boolean(),
  msg: z.string(),
  data: z.null(),
});

// Tweet
export const tweetScreenshotPayloadSchema = z.object({
  url: z.string().url(),
});

export const tweetScreenshotResponseSchema = z.object({
  id: z.string(),
  text: z.string(),
  comment: z.number(),
  retweet: z.number(),
  favorite: z.number(),
  bookmark: z.number(),
  views: z.string(),
  screenshot: z.string().url(),
});

export const jin10ResponseSchema = z
  .object({
    id: z.string(),
    time: z.string(),
    title: z.string(),
    hot: z.string(),
    type: z.string(),
    content: z.string(),
    screenshot: z.string().url(),
  })
  .array();

// Truth Social
export const truthSocialPayloadSchema = z.object({
  url: z.string().url(),
});

export const truthSocialResponseSchema = z.object({
  id: z.string(),
  text: z.string(),
  replay: z.number(),
  retruth: z.number(),
  like: z.number(),
  bookmark: z.number(),
  share: z.string(),
  screenshot: z.string().url(),
});

export const truthSocialThreadSchema = z.object({
  id: z.string(),
  created_at: z.date(),
  url: z.string().url(),
  content: z.string(),
  account: z.object({
    avatar: z.string().url(),
  }),
  media_attachments: z.array(
    z.object({
      id: z.string(),
      url: z.string().url(),
      preview_url: z.string().url(),
    })
  ),
  card: z.object({
    url: z.string().url(),
    title: z.string(),
    description: z.string(),
  }),
  replies_count: z.number(),
  reblogs_count: z.number(),
  favourites_count: z.number(),
  upvotes_count: z.number(),
  downvotes_count: z.number(),
});

export type TruthSocialThread = z.infer<typeof truthSocialThreadSchema>;

export const werssResponseSchema = z
  .object({
    id: z.string(),
    source: z.string(),
    url: z.string().url(),
    time: z.number(),
    title: z.string(),
    content: z.string(),
    pic_url: z.string().url(),
  })
  .array();
export type werssResponseSchema = z.infer<typeof werssResponseSchema>;

export const wallStreetResponseSchema = z
  .object({
    id: z.string(),
    timestamp: z.number(),
    title: z.string(),
    content: z.string(),
    screenshot: z.string().url(),
  })
  .array();
export type wallStreetResponseSchema = z.infer<typeof wallStreetResponseSchema>;

// HKEX
export const hkexPayloadSchema = z.object({
  screenshot: z.boolean().optional().default(false),
});

export const hkexNewsItemSchema = z.object({
  id: z.string(),
  title: z.string(),
  date: z.string(),
  category: z.string(),
  url: z.string().url(),
  isPdf: z.boolean().optional(),
});

export const hkexResponseSchema = z.object({
  news: z.array(hkexNewsItemSchema),
  screenshot: z.string().url().optional(),
  scrapedAt: z.string(),
});

export type HKEXPayload = z.infer<typeof hkexPayloadSchema>;
export type HKEXNewsItem = z.infer<typeof hkexNewsItemSchema>;
export type HKEXResponse = z.infer<typeof hkexResponseSchema>;
