import type { FastifyInstance } from 'fastify';

import {
  failedResponseSchema,
  successfulResponseSchema,
  werssResponseSchema,
} from '../../../schemas';

const REDIS_KEY = 'werss';

export async function werssRoutes(fastify: FastifyInstance): Promise<void> {
  fastify.get(
    '/werss',
    {
      schema: {
        response: {
          '2xx': successfulResponseSchema.extend({
            data: werssResponseSchema,
          }),
          '5xx': failedResponseSchema,
        },
      },
    },

    async (req, res) => {
      try {
        await getWeRssData();
        const cacheDataString = await global.fastify.redis.get(REDIS_KEY);
        if (!cacheDataString) {
          throw new Error(`${REDIS_KEY} has no cached data`);
        }
        const cachedData = JSON.parse(cacheDataString);
        res.status(200).send({
          err: false,
          msg: 'ok',
          data: cachedData,
        });
      } catch (error) {
        global.fastify.log.error(error);
        res.status(500).send({
          err: true,
          msg: error.message,
          data: null,
        });
      }
    }
  );
}

export const getWeRssData = async () => {
  global.fastify.log.info(`fetching werss articles`);
  const data: werssResponseSchema = [];
  const authToken = await getAuthToken();
  for (const key in sourceMap) {
    try {
      const articles = await getArticles(key, authToken);
      data.push(...articles);
    } catch (error) {
      global.fastify.log.error(`werss articles ${key} failed: ${error}`);
    }
  }
  global.fastify.log.info(
    `werss articles result: ${JSON.stringify(data.length)}`
  );
  await global.fastify.redis.set({ key: REDIS_KEY, data, ttl: 60 * 60 }); // cache for 1h
  return data;
};

const getAuthToken = async (): Promise<string> => {
  const url = 'https://we-mp-rss.natureselect.work/api/v1/wx/auth/token';
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
      username: 'admin',
      password: 'admin@123',
    }),
  });

  const result = await response.json();
  return result.access_token;
};

async function getArticles(
  key: string,
  authToken: string
): Promise<werssResponseSchema> {
  const { id, name, exclude } = sourceMap[key];
  const url = `https://we-mp-rss.natureselect.work/api/v1/wx/articles?offset=0&limit=10&search=&status=&mp_id=${id}`;
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      authorization: `Bearer ${authToken}`,
    },
  });

  const result = await response.json();
  const data = result.data.list.filter((d) => {
    const inOneHour = d.publish_time * 1000 > Date.now() - 60 * 60 * 1000;
    let isValidTitle = true;
    for (const w of filterWords) {
      if (d.title.includes(w)) {
        isValidTitle = false;
        break;
      }
    }

    for (const w of exclude) {
      if (d.title.includes(w)) {
        isValidTitle = false;
        break;
      }
    }
    return inOneHour && isValidTitle;
  });

  const articles: werssResponseSchema = [];
  for (const d of data) {
    global.fastify.log.info(`fetching werss article: ${d.url}`);
    const res = await global.fastify.playwright.getWeRssContentByURL(d.url);
    if (!res) {
      global.fastify.log.error(`failed to fetch content for ${d.url}`);
      await notifyFeishu(`爬取微信文章失败：${d.url}`);
      continue;
    }
    const { content, pic_url } = res;
    const article = {
      id: d.id,
      source: name,
      url: d.url,
      time: d.publish_time * 1_000,
      title: d.title,
      content,
      pic_url,
    };
    articles.push(article);
  }

  return articles;
}

export const notifyFeishu = async (notifyText: string) => {
  const url =
    'https://open.feishu.cn/open-apis/bot/v2/hook/d43d416f-ef3e-42c5-b3f9-1e31b773e855';

  const response = await fetch(url, {
    method: 'POST',
    body: JSON.stringify({
      msg_type: 'text',
      content: { text: notifyText },
    }),
  });

  const result = await response.json();
  if (!result.ok) {
    global.fastify.log.error(
      `notify feishu robot error ${JSON.stringify(result)}`
    );
  }
};

const sourceMap = {
  Liangziwei: {
    name: '量子位',
    id: 'MP_WXS_3236757533',
    exclude: [],
  },
  Unicorn: {
    name: '海外独角兽',
    id: 'MP_WXS_3869640945',
    exclude: [],
  },
  Machineheart: {
    name: '机器之心',
    id: 'MP_WXS_3073282833',
    exclude: [],
  },
  Xinzhiyuan: {
    name: '新智元',
    id: 'MP_WXS_3271041950',
    exclude: [],
  },
  Guixingren: {
    name: '硅星人Pro',
    id: 'MP_WXS_3926568365',
    exclude: [],
  },
  Xinhuashe: {
    name: '新华社',
    id: 'MP_WXS_3084276724',
    exclude: ['夜读', '早知天下事'],
  },
  Latepost: {
    name: '晚点LatePost',
    id: 'MP_WXS_3572959446',
    exclude: [],
  },
  Geekpark: {
    name: '极客公园',
    id: 'MP_WXS_1304308441',
    exclude: ['极客早知道'],
  },
  FounderPark: {
    name: 'Founder Park',
    id: 'MP_WXS_3895742803',
    exclude: [],
  },
  Zhinengyongxian: {
    name: '智能涌现',
    id: 'MP_WXS_3900464567',
    exclude: [],
  },
} as const;

const filterWords = [
  '优惠',
  '抽奖',
  '打折',
  '特价',
  '秒杀',
  '抢购',
  '限时折扣',
  '立即购买',
  '赠品',
  '独家优惠',
  '促销',
  '大促',
  '品牌',
  '销量',
  '销售',
  '新品',
  '热销',
  '热卖',
  '必买',
  '购买链接',
  '秒杀',
  '推荐购买',
  '强烈推荐',
  '招聘',
  '应聘',
  '岗位',
  '职位',
  '加入我们',
  '工作机会',
  '招贤纳士',
  '招聘信息',
  '投资',
  '理财',
  '股票',
  '基金',
  '稳赚',
  '赚大钱',
  '高收益',
  '暴利',
  '快速赚钱',
  'APP下载',
  '注册送红包',
  '邀请好友',
  '安装即得',
  '下载即送',
  '免费注册',
  '赢大奖',
  '轻松赚钱',
  '无风险',
  '必定成功',
  '稳赚不赔',
  '包赚',
  '稳赚的方法',
  '活动',
  '大礼包',
  '最后机会',
  '最后一天',
  '倒计时',
  '火热进行中',
];
