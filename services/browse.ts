declare module 'fastify' {
  interface FastifyInstance {
    browser: SteelService;
  }
}

import {
    chromium,
    type Browser,
    type BrowserContext,
    type Page,
} from 'playwright';
import Steel from 'steel-sdk';

import fs from 'node:fs';
import path from 'node:path';
import type { Session } from 'steel-sdk/resources/index.mjs';
import { log } from '../services/log';
import { sleep } from '../services/utils';
const __dirname = path.dirname(new URL(import.meta.url).pathname);

export class SteelService {
  private client: Steel | null = null;
  private session: Session | null = null;
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private page: Page | null = null;
  private screenshotDir: string;

  constructor() {
    this.screenshotDir = path.join(__dirname, '../public/screenshots');
    // Create screenshots directory if it doesn't exist
    if (!fs.existsSync(this.screenshotDir)) {
      fs.mkdirSync(this.screenshotDir, { recursive: true });
    }
  }

  async init() {
    if (this.browser) {
      log.info('Browser instance already exists, reusing...');
      return this;
    }

    const client = new Steel({
      steelAPIKey: process.env.STEEL_API_KEY,
      baseURL: process.env.STEEL_BASE_URL,
    });

    log.info('Creating Steel session...');

    // Create a new Steel session with all available options
    this.session = await client.sessions.create({
      timeout: 7 * 24 * 3_600_000,
      blockAds: true,
      concurrency: 1,
    });

    // Connect Playwright to the Steel session
    this.browser = await chromium.connectOverCDP(
      `wss://${process.env.STEEL_BASE_URL?.split('://').at(-1)}/?apiKey=${
        process.env.STEEL_API_KEY
      }&sessionId=${this.session.id}`
    );

    log.info('Connected to browser via Playwright');

    // Create initial context and page
    this.context = this.browser.contexts()[0];
    this.page = this.context.pages()[0];

    return this;
  }

  async createNewPage() {
    log.info('Creating new page...');
    if (!this.session || !this.browser) {
      log.error('Browser session not exist or expired.');
      await this.restart();
    }
    if (!this.context) {
      this.context = (await this.browser?.newContext()) ?? null;
    }

    this.page = (await this.context?.newPage()) ?? null;

    return this.page;
  }

  async close() {
    // Release a Steel session
    if (this.client && this.session) {
      await this.client.sessions.release(this.session.id);
    }

    this.client = null;
    this.session = null;
    this.browser = null;
    this.context = null;
    this.page = null;
  }

  async restart() {
    log.info('Restarting browser session...');
    await this.close();
    await this.init();
    log.info('Browser session restarted');

    // Update queue worker's browser reference if it exists
    if (global.fastify?.worker) {
      global.fastify.worker.updateBrowser(this);
    }
  }

  async screenshot(payload: { url: string }) {
    if (!this.session || !this.browser || !this.browser.isConnected()) {
      log.error('Browser session not exist or expired.');
      await this.restart();
    }

    if (!this.context) {
      log.info('Creating new context ...');
      this.context = (await this.browser?.newContext()) ?? null;
    }

    if (!this.page || this.page.isClosed()) {
      log.info('Creating new page ...');
      this.page = (await this.context?.newPage()) ?? null;
    }

    try {
      log.info(`Navigating to ${payload.url}...`);
      if (!this.page) {
        throw new Error('Failed to create page');
      }
      // await this.createNewPage();
      await this.page.goto(payload.url, {
        waitUntil: 'networkidle',
        // waitUntil: "domcontentloaded",
      });

      // Ensure the page is active and focused
      await this.page.bringToFront();

      const body = await this.page.$('body');
      if (body) {
        const bodyScreenshotName = crypto.randomUUID();
        await body.screenshot({
          path: path.join(this.screenshotDir, `${bodyScreenshotName}.png`),
        });
        log.info('HTML body screenshot captured:', `${bodyScreenshotName}.png`);
      } else {
        log.error("Can't find the body element in HTML");
      }
    } catch (error) {
      log.error('Error during browsing or screenshot capture:');
      log.error(error);
      await this.restart();
    }
  }

  async screenshotTwitter(payload: { url: string }) {
    if (!this.session || !this.browser || !this.browser.isConnected()) {
      log.error('Browser session not exist or expired.');
      await this.restart();
    }

    if (!this.context) {
      log.info('Creating new context ...');
      this.context = (await this.browser?.newContext()) ?? null;
    }

    if (!this.page || this.page.isClosed()) {
      log.info('Creating new page ...');
      this.page = (await this.context?.newPage()) ?? null;
    }

    try {
      log.info(`Navigating to ${payload.url}...`);
      if (!this.page) {
        throw new Error('Failed to create page');
      }
      await this.page.goto(payload.url, {
        waitUntil: 'networkidle',
        timeout: 30000, // Add timeout to prevent hanging
      });

      // Tweet root element
      const article = await this.page.$('article');
      if (!article) {
        log.error('Tweet not found');
        return null;
      }

      // Parse the tweet data
      const tweetFooterElement = article
        ? await article.$('div[aria-label][role="group"]')
        : null;
      const tweetFooterLabel = tweetFooterElement
        ? await tweetFooterElement.getAttribute('aria-label')
        : '';
      const tweetFooterText = tweetFooterLabel?.split(', ');

      const [comment, retweet, favorite, bookmark] = [
        Number.parseInt(tweetFooterText?.at(0) || '0'),
        Number.parseInt(tweetFooterText?.at(1) || '0'),
        Number.parseInt(tweetFooterText?.at(2) || '0'),
        Number.parseInt(tweetFooterText?.at(3) || '0'),
      ];

      const viewsElement = article
        ? await article.$('span[data-testid=app-text-transition-container]')
        : null;
      const views = await viewsElement?.textContent();

      const linkElement = article
        ? await article.$('a[aria-describedby]')
        : null;
      const linkHref = linkElement
        ? await linkElement.getAttribute('href')
        : '';
      const id = linkHref?.split('/').at(-1) || '';

      const tweetTextElement = article
        ? await article.$('div[data-testid=tweetText]')
        : null;
      const tweetText = tweetTextElement
        ? await tweetTextElement.textContent()
        : '';

      // Screenshot
      // Hide tweet footer element if it exists
      if (tweetFooterElement) {
        await this.page.evaluate((el) => {
          el.style.display = 'none';
        }, tweetFooterElement);
      }

      const screenshotName = crypto.randomUUID();
      await article.screenshot({
        path: path.join(this.screenshotDir, `${screenshotName}.png`),
      });
      log.info('Tweet text screenshot captured:', `${screenshotName}.png`);

      // Return the tweet data
      const items: {
        id: string;
        text: string;
        comment: number;
        retweet: number;
        favorite: number;
        bookmark: number;
        views: string;
        screenshot: string;
      } = {
        id,
        text: tweetText || '',
        comment,
        retweet,
        favorite,
        bookmark,
        views: views || '',
        screenshot: `${screenshotName}.png`,
      };

      return items;
    } catch (error) {
      log.error('Error during browsing or screenshot capture:');
      log.error(error);
      await this.restart();
    }
  }

  async getTruthSocialFeeds(payload: { url: string }) {
    if (!this.session || !this.browser || !this.browser.isConnected()) {
      log.error('Browser session not exist or expired.');
      await this.restart();
    }

    if (!this.context) {
      log.info('Creating new context ...');
      this.context = (await this.browser?.newContext()) ?? null;
    }

    if (!this.page || this.page.isClosed()) {
      log.info('Creating new page ...');
      this.page = (await this.context?.newPage()) ?? null;
    }

    try {
      log.info(`Navigating to ${payload.url}...`);
      if (!this.page) {
        throw new Error('Failed to create page');
      }

      await this.page.goto(payload.url, {
        waitUntil: 'domcontentloaded',
      });

      const preElement = await this.page.$('pre');
      if (!preElement) return null;
      const textContent = await preElement.textContent();
      if (!textContent) return null;
      return JSON.parse(textContent);
    } catch (error) {
      log.error('Error during browsing or screenshot capture:');
      log.error(error);
      await this.restart();
    }
  }

  async screenshotTruthSocial(payload: { url: string }) {
    if (!this.session || !this.browser || !this.browser.isConnected()) {
      log.error('Browser session not exist or expired.');
      await this.restart();
    }

    if (!this.context) {
      log.info('Creating new context ...');
      this.context = (await this.browser?.newContext()) ?? null;
    }

    if (!this.page || this.page.isClosed()) {
      log.info('Creating new page ...');
      this.page = (await this.context?.newPage()) ?? null;
    }

    try {
      log.info(`Navigating to ${payload.url}...`);
      if (!this.page) {
        throw new Error('Failed to create page');
      }
      await sleep(Math.random() * 3_000);
      await this.page.goto(payload.url, {
        waitUntil: 'networkidle',
        timeout: 30000, // Add timeout to prevent hanging
      });

      // thread root element
      const thread = await this.page.$('.thread');
      if (!thread) {
        log.error('TruthSocial thread not found');
        return null;
      }
      const threadScreenshotName = crypto.randomUUID();

      const statusBarElement = thread
        ? await thread.$('div[data-testid=status-action-bar]')
        : '';
      // Hide status bar element if it exists
      if (statusBarElement) {
        await this.page.evaluate((el) => {
          el.style.display = 'none';
        }, statusBarElement);
      }

      const ctaBannerElement = thread
        ? await this.page.$('div[data-testid=banner]')
        : '';
      // Hide cta banner element if it exists
      if (ctaBannerElement) {
        await this.page.evaluate((el) => {
          el.style.display = 'none';
        }, ctaBannerElement);
      }

      await thread?.screenshot({
        path: path.join(this.screenshotDir, `${threadScreenshotName}.png`),
      });
      log.info(
        'TruthSoical thread screenshot captured:',
        `${threadScreenshotName}.png`
      );

      return {
        screenshot: `${threadScreenshotName}.png`,
      };
    } catch (error) {
      log.error('Error during browsing or screenshot capture:');
      log.error(error);
      await this.restart();
    }
  }
}
