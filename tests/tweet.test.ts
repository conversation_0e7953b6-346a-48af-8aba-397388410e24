import { expect, test } from 'vitest';
import { requestBrowseService } from '../services/utils';

const host =
  process.env.NODE_ENV === 'development'
    ? 'http://127.0.0.1:8123'
    : 'https://defomo-x-crawler-api.dev.cn.defomo.hony.love';

test('request to tweet screenshot route', async () => {
  const result = await requestBrowseService({
    host: `${host}/api/v1/tweet/screenshot/generate`,
    url: 'https://x.com/bbssppllvv/status/1932325303273271727',
  });
  expect(result.screenshot).toBeDefined();
});

test('batch requests to tweet screenshot route', async () => {
  const urls = [
    'https://x.com/bbssppllvv/status/1932325303273271727',
    'https://x.com/Larry_LiDev/status/1566752506541404162',
    'https://x.com/cloudwu/status/1932625836001374420',
    'https://x.com/diabrowser/status/1932588756382384310',
    'https://x.com/512x512/status/1923893565664657418',
  ];
  const results = await Promise.all(
    urls.map((url) =>
      requestBrowseService({
        host: `${host}/api/v1/tweet/screenshot/generate`,
        url,
      })
    )
  );
  expect(results).toBeDefined();
  expect(results.length).toBe(urls.length);
  results.forEach((result, idx) => {
    expect(result).toBeDefined();
    expect(result.screenshot).toBeDefined();
  });
});
