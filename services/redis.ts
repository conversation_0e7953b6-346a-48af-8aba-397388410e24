declare module 'fastify' {
  interface FastifyInstance {
    redis: RedisService;
  }
}

import { Redis } from 'ioredis';

export class RedisService {
  public client: Redis | null;

  constructor() {
    this.client = null;
  }

  init() {
    this.client = new Redis({
      host: process.env.REDIS_HOST,
      db: 0,
      port: Number.parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD || undefined,
      maxRetriesPerRequest: null,
    });
    // return this.client;
  }

  async close() {
    if (this.client) {
      await this.client.quit();
    }
  }

  async restart() {
    if (this.client) {
      await this.client.quit();
      this.init();
    }
  }

  async get(key: string) {
    if (!this.client) {
      global.fastify.log.error(
        `redis is not initialized, failed to fetch result for key ${key}`
      );
      return null;
    }
    const result = await this.client.get(key);
    return result;
  }

  async set({
    key,
    data,
    ttl,
  }: {
    key: string;
    data: any;
    ttl?: number | undefined;
  }) {
    if (!this.client) {
      global.fastify.log.error(
        `redis is not initialized, failed to fetch result for key ${key}`
      );
      return null;
    }
    const value = typeof data === 'string' ? data : JSON.stringify(data);

    if (ttl) {
      return await this.client.set(key, value, 'EX', ttl);
    }

    return await this.client.set(key, value);
  }
}
