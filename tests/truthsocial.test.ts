import { assert, expect, test } from 'vitest';
import { requestBrowseService } from '../services/utils';

const host =
  process.env.NODE_ENV === 'development'
    ? 'http://**************:8123'
    : 'https://defomo-x-crawler-api.dev.cn.defomo.hony.love';

test('request to truthsocial screenshot route', async () => {
  const result = await requestBrowseService({
    host: `${host}/api/v1/truthsocial/screenshot/generate`,
    url: 'https://truthsocial.com/@realDonaldTrump/posts/114656580327959703',
  });
  expect(result.screenshot).toBeDefined();
});

test('batch requests to truthsocial screenshot route', async () => {
  const urls = [
    'https://truthsocial.com/@realDonaldTrump/posts/114656580327959703',
    'https://truthsocial.com/@realDonaldTrump/posts/114701422214044562',
    'https://truthsocial.com/@realDonaldTrump/posts/114701409149757820',
    'https://truthsocial.com/@realDonaldTrump/posts/114698784693311065',
    'https://truthsocial.com/@realDonaldTrump/posts/114697034400754530',
  ];
  const results = await Promise.all(
    urls.map((url) =>
      requestBrowseService({
        host: `${host}/api/v1/truthsocial/screenshot/generate`,
        url,
      })
    )
  );
  expect(results).toBeDefined();
  expect(results.length).toBe(urls.length);
  results.forEach((result, idx) => {
    expect(result).toBeDefined();
    expect(result.screenshot).toBeDefined();
  });
});

test('request to truthsocial feeds route', async () => {
  const result = await fetch(`${host}/api/v1/truthsocial`).then((res) =>
    res.json()
  );
  expect(result.data.length).toBe(20);
  expect(result.data[0].id).toBeDefined();
});

test('request to truthsocial thread route', async () => {
  const result = await fetch(
    `${host}/api/v1/truthsocial?url=https://truthsocial.com/@realDonaldTrump/posts/114697034400754530`
  ).then((res) => res.json());
  expect(result.data.id).toBeDefined();
});
