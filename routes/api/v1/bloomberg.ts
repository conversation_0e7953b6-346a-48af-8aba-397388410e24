import { load } from 'cheerio';
import type { FastifyInstance } from 'fastify';
import fs from 'fs/promises';
import path from 'node:path';

import {
    bloombergResponseSchema,
    failedResponseSchema,
    successfulResponseSchema,
} from '../../../schemas';
import { Bloomberg } from '../../../services/bloomberg';

export async function bloombergRoutes(fastify: FastifyInstance): Promise<void> {
  fastify.get(
    '/bloomberg',
    {
      schema: {
        response: {
          '2xx': successfulResponseSchema.extend({
            data: bloombergResponseSchema,
          }),
          '5xx': failedResponseSchema,
        },
      },
    },

    async (req, res) => {
      try {
        const data = await getBloombergNews(fastify);
        res.status(200).send({
          err: false,
          msg: 'ok',
          data,
        });
      } catch (error) {
        res.status(500).send({
          err: true,
          msg: error.message,
          data: null,
        });
      }
    }
  );
}

function cleanContent(input: string): string {
  try {
    const isHtml = /<\/?[a-z][\s\S]*>/i.test(input);
    if (!isHtml) return input;

    const $ = load(input);
    return $.text().trim();
  } catch (error) {
    return '';
  }
}

function parseTimeAgo(timeText: string): number {
  const now = Date.now();
  const timeStr = timeText.toLowerCase().trim();
  
  if (timeStr.includes('min ago')) {
    const minutes = parseInt(timeStr.match(/(\d+)\s*min/)?.[1] || '0');
    return now - (minutes * 60 * 1000);
  } else if (timeStr.includes('hr ago') || timeStr.includes('hour ago')) {
    const hours = parseInt(timeStr.match(/(\d+)\s*hr/)?.[1] || '0');
    return now - (hours * 60 * 60 * 1000);
  } else if (timeStr.includes('day ago')) {
    const days = parseInt(timeStr.match(/(\d+)\s*day/)?.[1] || '0');
    return now - (days * 24 * 60 * 60 * 1000);
  }
  
  // Default to current time if can't parse
  return now;
}

async function getBloombergNews(fastify: FastifyInstance) {
  const url = 'https://www.bloomberg.com/latest';

  // Use Playwright to fetch the page content to avoid 403 errors
  const context = await fastify.playwright.createContext();
  const page = await context.newPage();

  try {
    // Navigate to the page
    await page.goto(url, {
      waitUntil: 'domcontentloaded',
      timeout: 30000,
    });

    // Wait for content to load
    await page.waitForTimeout(3000);

    // Get the HTML content
    const html = await page.content();

    // Close the page and context
    await page.close();
    await context.close();

    const $ = load(html);

    const newsItems: any[] = [];

  // Find news items in the latest news section
  $('[data-module="LatestNews"] article, .latest-news article, article').each((index, element) => {
    const $article = $(element);
    
    // Extract time
    const timeElement = $article.find('time');
    const timeText = timeElement.text().trim();
    if (!timeText) return; // Skip if no time found
    
    // Extract title and URL
    const titleLink = $article.find('a[href*="/news/articles/"]').first();
    const title = titleLink.text().trim();
    const relativeUrl = titleLink.attr('href');
    
    if (!title || !relativeUrl) return; // Skip if no title or URL
    
    const fullUrl = relativeUrl.startsWith('http') ? relativeUrl : `https://www.bloomberg.com${relativeUrl}`;
    
    // Extract article ID from URL
    const urlMatch = relativeUrl.match(/\/news\/articles\/(\d{4}-\d{2}-\d{2})\/(.+)/);
    if (!urlMatch) return;
    
    const [, date, slug] = urlMatch;
    const id = `${date}-${slug}`;
    
    // Parse timestamp
    const timestamp = parseTimeAgo(timeText);
    
    // Extract summary/description if available
    const summary = $article.find('p, .summary, .description').first().text().trim();
    
    // Skip opinion articles for now (they have different structure)
    if (title.toLowerCase().includes('opinion |')) return;
    
    newsItems.push({
      id,
      timestamp,
      title: cleanContent(title),
      content: cleanContent(summary || title),
      url: fullUrl,
      timeText,
    });
  });

  } catch (error) {
    await page.close();
    await context.close();
    throw new Error(`Failed to fetch Bloomberg news: ${error.message}`);
  }

  // Remove duplicates and sort by timestamp (newest first)
  const uniqueNews = newsItems
    .filter((item, index, self) => 
      index === self.findIndex(t => t.id === item.id)
    )
    .sort((a, b) => b.timestamp - a.timestamp)
    .slice(0, 20); // Limit to 20 most recent items

  // Generate screenshots for each news item
  const results: any[] = [];
  for (const newsItem of uniqueNews) {
    try {
      const screenshotId = crypto.randomUUID();
      await Bloomberg.screenshot({ id: screenshotId, uri: newsItem.url });

      // Upload to S3 bucket
      await fastify.s3.uploadObject(
        `screenshot/bloomberg/${screenshotId}.png`,
        path.join('./public/screenshots', `${screenshotId}.png`)
      );
      
      // Remove local files
      await fs.unlink(path.join('./public/screenshots', `${screenshotId}.png`));

      const screenshot = `https://${process.env.S3_BUCKET}.${process.env.S3_ENDPOINT}/screenshot/bloomberg/${screenshotId}.png`;
      
      results.push({
        id: newsItem.id,
        timestamp: newsItem.timestamp,
        title: newsItem.title,
        content: newsItem.content,
        screenshot,
      });
    } catch (error) {
      console.error(`Failed to process news item ${newsItem.id}:`, error);
      // Continue with other items even if one fails
    }
  }

  return results;
}
