import type { FastifyInstance } from 'fastify';
import z from 'zod';
import path from 'node:path';

import {
  truthSocialPayloadSchema,
  truthSocialResponseSchema,
  successfulResponseSchema,
  badRequestResponseSchema,
  failedResponseSchema,
  truthSocialThreadSchema,
} from '../../../schemas';

// Declare routes
export async function truthSocialRoutes(
  fastify: FastifyInstance
): Promise<void> {
  fastify.get(
    '/truthsocial',
    {
      schema: {
        querystring: z
          .object({
            url: z.string().optional(),
          })
          .optional(),
        response: {
          // '2xx': successfulResponseSchema.extend({
          //   data: truthSocialThreadSchema,
          // }),
          '4xx': badRequestResponseSchema,
          '5xx': failedResponseSchema,
        },
      },
    },
    async (req, res) => {
      try {
        const { url } = req.query;

        if (!url) {
          // get all list
          const apiUrl =
            'https://truthsocial.com/api/v1/accounts/107780257626128497/statuses?exclude_replies=true&only_replies=false&with_muted=true';
          const result = await fastify.browser.getTruthSocialFeeds({
            url: apiUrl,
          });

          if (!result) {
            res.status(400).send({
              err: true,
              msg: 'Fetch TruthSocial failed',
              data: null,
            });
            return;
          }
          res.status(200).send({
            err: false,
            msg: 'ok',
            data: result,
          });
        } else {
          // fetch url data
          const apiUrl = `https://truthsocial.com/api/v1/statuses/${url
            .split('/')
            .at(-1)}`;
          const result = await fastify.browser.getTruthSocialFeeds({
            url: apiUrl,
          });

          if (!result) {
            res.status(400).send({
              err: true,
              msg: 'Fetch TruthSocial thread failed',
              data: null,
            });
            return;
          }
          res.status(200).send({
            err: false,
            msg: 'ok',
            data: result,
          });
        }
      } catch (error) {
        fastify.log.error(error);
        res.status(500).send({
          err: false,
          msg: error.message,
          data: null,
        });
      }
    }
  );

  fastify.post(
    '/truthsocial/screenshot/generate',
    {
      schema: {
        body: truthSocialPayloadSchema,
        response: {
          '2xx': successfulResponseSchema.extend({
            data: truthSocialResponseSchema.pick({ screenshot: true }),
          }),
          '4xx': badRequestResponseSchema,
          '5xx': failedResponseSchema,
        },
      },
    },
    async (req, res) => {
      try {
        const { url } = req.body as z.infer<typeof truthSocialPayloadSchema>;

        if (!url) {
          res.status(400).send({
            err: true,
            msg: 'Invalid URL',
            data: null,
          });
          return;
        }

        // add a job to the queue
        try {
          const result = await fastify.queue.addAndWait(
            'screenshotTruthSocial',
            {
              url,
              channel: 'truthsocial',
              requestedAt: new Date().toISOString(),
            },
            120_000
          );

          const truthsocial = result.result?.screenshot;

          if (!truthsocial) {
            res.status(400).send({
              err: true,
              msg: 'Fetch truthsocial thread failed',
              data: null,
            });
            return;
          }

          // upload to s3 bucket
          await fastify.s3.uploadObject(
            `screenshot/x/${truthsocial?.screenshot}`,
            path.join('./public/screenshots', `${truthsocial?.screenshot}`)
          );
          truthsocial.screenshot = `https://${process.env.S3_BUCKET}.${process.env.S3_ENDPOINT}/screenshot/x/${truthsocial?.screenshot}`;

          res.status(200).send({
            err: false,
            msg: 'Screenshot generated successfully',
            data: result.result?.screenshot,
          });
        } catch (error) {
          fastify.log.error('Queue job error:');
          fastify.log.error(error);
          res.status(500).send({
            err: true,
            msg: error.message || 'Failed to process screenshot request',
            data: null,
          });
        }
      } catch (error) {
        fastify.log.error(error);
        res.status(500).send({
          err: false,
          msg: error.message,
          data: null,
        });
      }
    }
  );
}
