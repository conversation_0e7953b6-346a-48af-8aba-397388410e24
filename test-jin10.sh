#!/bin/bash

# Jin10 API 测试脚本
# 使用方法: ./test-jin10.sh

HOST="http://127.0.0.1:8123"
ENDPOINT="/api/v1/jin10"
URL="${HOST}${ENDPOINT}"

echo "=== 测试 Jin10 API ==="
echo "URL: $URL"
echo "时间: $(date)"
echo ""

# 发送请求并保存响应
echo "发送请求中..."
response=$(curl -s -w "\n%{http_code}" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  "$URL")

# 分离响应体和状态码
http_code=$(echo "$response" | tail -n1)
response_body=$(echo "$response" | head -n -1)

echo "HTTP 状态码: $http_code"
echo ""

if [ "$http_code" = "200" ]; then
    echo "✅ 请求成功!"
    echo ""
    echo "响应数据:"
    echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"
    
    # 保存响应到文件
    timestamp=$(date +"%Y%m%d_%H%M%S")
    output_file="jin10_response_${timestamp}.json"
    echo "$response_body" > "$output_file"
    echo ""
    echo "📁 响应已保存到: $output_file"
    
    # 提取关键信息
    echo ""
    echo "=== 数据摘要 ==="
    echo "$response_body" | jq -r '
        "总新闻数量: " + (.data | length | tostring) +
        "\n加密货币新闻: " + ([.data[] | select(.type == "crypto")] | length | tostring) +
        "\n科技新闻: " + ([.data[] | select(.type == "tech")] | length | tostring)
    ' 2>/dev/null || echo "无法解析 JSON 数据"
    
else
    echo "❌ 请求失败!"
    echo "响应内容:"
    echo "$response_body"
fi

echo ""
echo "=== 测试完成 ==="
