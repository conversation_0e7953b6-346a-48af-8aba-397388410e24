import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, chromium, <PERSON> } from 'playwright';
import path from 'node:path';

export class Bloomberg {
  private static browser: Browser;
  private static context: BrowserContext;
  private static page: Page;
  private static screenshotDir: string;

  public static async init() {
    this.screenshotDir = 'public/screenshots';
    this.browser = await chromium.launch({
      headless: true,
      // Comprehensive args to simulate real Chrome browser and avoid detection
      args: [
        '--disable-dev-shm-usage', // Prevents OOM in limited memory environments
        '--no-sandbox', // Required in some restricted environments
        '--disable-setuid-sandbox',
        '--disable-gpu', // Reduces resource usage
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-blink-features=AutomationControlled',
        '--disable-infobars',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-images',
        '--disable-default-apps',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-field-trial-config',
        '--disable-back-forward-cache',
        '--disable-ipc-flooding-protection',
        '--no-first-run',
        '--no-default-browser-check',
        '--no-zygote',
        '--single-process',
        '--disable-dev-tools',
        '--disable-extensions-file-access-check',
        '--disable-extensions-http-throttling',
        '--disable-component-extensions-with-background-pages',
        '--disable-default-apps',
        '--mute-audio',
        '--no-startup-window',
        '--disable-breakpad',
        '--disable-component-update',
        '--disable-domain-reliability',
        '--disable-sync',
        '--disable-client-side-phishing-detection',
        '--disable-hang-monitor',
        '--disable-popup-blocking',
        '--disable-prompt-on-repost',
        '--disable-background-networking',
        '--disable-background-timer-throttling',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows',
        '--force-fieldtrials=*BackgroundTracing/default/',
        '--enable-features=NetworkService,NetworkServiceInProcess',
        '--disable-features=TranslateUI,VizDisplayCompositor',
        '--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      ],
    });
    
    this.context = await this.browser.newContext({
      viewport: { width: 1920, height: 1080 },
      deviceScaleFactor: 2,
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      extraHTTPHeaders: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
      },
    });
  }

  public static async screenshot({ id, uri }: { id: string; uri: string }) {
    if (!this.browser || !this.context) {
      await this.init();
    }
    const page = await this.context.newPage();

    await page.goto(uri, {
      waitUntil: 'domcontentloaded',
    });
    await page.waitForTimeout(2000);
    
    // Wait for the main content to load
    await page.waitForSelector('article, .story-body, .article-body', { timeout: 10000 });

    // Remove unwanted elements
    await page.evaluate(() => {
      // Remove ads, navigation, footer, etc.
      const selectorsToRemove = [
        'nav',
        'header',
        'footer',
        '.advertisement',
        '.ad-container',
        '.newsletter-signup',
        '.social-share',
        '.related-articles',
        '.comments',
        '.paywall',
        '.subscription-banner',
        '[data-module="Advertisement"]',
        '[data-module="PaywallBanner"]',
        '.bb-nav',
        '.bb-footer',
        '.bb-header',
      ];
      
      selectorsToRemove.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => el.remove());
      });

      // Create a wrapper for the main content
      const article = document.querySelector('article') || 
                     document.querySelector('.story-body') || 
                     document.querySelector('.article-body') ||
                     document.querySelector('main');
      
      if (article) {
        const wrapper = document.createElement('div');
        wrapper.id = 'capture-wrapper';
        wrapper.setAttribute(
          'style',
          `
            background-color: #fff;
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          `
        );

        article.parentNode?.insertBefore(wrapper, article);
        wrapper.appendChild(article);
      }
    });

    const wrapper = await page.$('#capture-wrapper');
    if (wrapper) {
      await wrapper.screenshot({ path: `${this.screenshotDir}/${id}.png` });
    } else {
      // Fallback: screenshot the whole page
      await page.screenshot({ path: `${this.screenshotDir}/${id}.png`, fullPage: true });
    }
    
    await page.close();
  }

  public static formatTime(timestamp: number) {
    const date = new Date(timestamp);
    return date.toLocaleString('sv-SE', {
      timeZone: 'Asia/Shanghai',
    });
  }

  private static timestampToISO(timestamp: number) {
    return (
      new Date(timestamp)
        .toLocaleString('sv-SE', {
          timeZone: 'Asia/Shanghai',
        })
        .replace(' ', 'T') + '.000+08:00'
    );
  }

  public static async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}
