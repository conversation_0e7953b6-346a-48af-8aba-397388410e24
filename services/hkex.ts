import { load } from 'cheerio';
import type { Page } from 'playwright';
import { log } from './log';

export interface HKEXNewsItem {
  id: string;
  title: string;
  date: string;
  category: string;
  url: string;
  isPdf?: boolean;
}

export class HKEXService {
  private static instance: HKEXService;
  private baseUrl = 'https://www.hkex.com.hk';

  static getInstance(): HKEXService {
    if (!HKEXService.instance) {
      HKEXService.instance = new HKEXService();
    }
    return HKEXService.instance;
  }

  async scrapeNewsReleases(page: Page): Promise<HKEXNewsItem[]> {
    try {
      log.info('Starting HKEX news scraping...');
      
      // Navigate to the news releases page
      await page.goto('https://www.hkex.com.hk/News/News-Release?sc_lang=en', {
        waitUntil: 'networkidle',
        timeout: 30000,
      });

      // Wait for the news content to load
      await page.waitForSelector('.news-releases-list, .news-item, [data-testid="news-item"]', {
        timeout: 10000,
      }).catch(() => {
        log.warn('News container not found with specific selectors, trying generic approach');
      });

      // Get page content and parse with cheerio
      const content = await page.content();
      const $ = load(content);
      
      const newsItems: HKEXNewsItem[] = [];
      
      // Look for news items in various possible structures
      const selectors = [
        '.news-item',
        '[data-testid="news-item"]',
        '.news-releases-list .item',
        '.news-list-item',
        'article',
        '.content-item'
      ];

      let foundItems = false;
      
      for (const selector of selectors) {
        const items = $(selector);
        if (items.length > 0) {
          log.info(`Found ${items.length} news items with selector: ${selector}`);
          foundItems = true;
          
          items.each((index, element) => {
            const $item = $(element);
            
            // Try to extract news information
            const titleElement = $item.find('a[href*="/News/"], a[href*="news"], h1, h2, h3, .title').first();
            const dateElement = $item.find('.date, .time, .published, [class*="date"]').first();
            const categoryElement = $item.find('.category, .type, .tag, [class*="category"]').first();
            
            const title = titleElement.text().trim();
            const href = titleElement.attr('href');
            
            if (title && href) {
              const fullUrl = href.startsWith('http') ? href : `${this.baseUrl}${href}`;
              const date = dateElement.text().trim() || new Date().toISOString().split('T')[0];
              const category = categoryElement.text().trim() || 'Corporate';
              const isPdf = href.toLowerCase().includes('.pdf') || title.toLowerCase().includes('pdf');
              
              // Generate a simple ID from URL or title
              const id = href.split('/').pop()?.replace('.pdf', '') || 
                         title.toLowerCase().replace(/[^a-z0-9]/g, '-').substring(0, 50);
              
              newsItems.push({
                id,
                title,
                date,
                category,
                url: fullUrl,
                isPdf,
              });
            }
          });
          break; // Stop after finding items with first working selector
        }
      }

      // If no structured news items found, try to extract from text patterns
      if (!foundItems) {
        log.info('No structured news items found, trying text pattern matching...');
        
        // Look for date patterns and nearby links
        const textContent = $.text();
        const dateRegex = /(\d{1,2}\s+(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)\s+\d{4})/gi;
        const dates = textContent.match(dateRegex) || [];
        
        // Find all links that might be news items
        $('a[href*="/News/"], a[href*="news"]').each((index, element) => {
          const $link = $(element);
          const href = $link.attr('href');
          const title = $link.text().trim();
          
          if (title && href && title.length > 10) {
            const fullUrl = href.startsWith('http') ? href : `${this.baseUrl}${href}`;
            const isPdf = href.toLowerCase().includes('.pdf');
            const id = href.split('/').pop()?.replace('.pdf', '') || 
                       title.toLowerCase().replace(/[^a-z0-9]/g, '-').substring(0, 50);
            
            // Try to find a nearby date
            const nearbyText = $link.parent().text() + ' ' + $link.closest('div, article, section').text();
            const dateMatch = nearbyText.match(dateRegex);
            const date = dateMatch ? dateMatch[0] : new Date().toISOString().split('T')[0];
            
            newsItems.push({
              id,
              title,
              date,
              category: 'Corporate',
              url: fullUrl,
              isPdf,
            });
          }
        });
      }

      // Remove duplicates based on URL
      const uniqueNews = newsItems.filter((item, index, self) => 
        index === self.findIndex(t => t.url === item.url)
      );
      console.log(`Found ${uniqueNews.length} unique HKEX news items`);
      log.info(`Successfully scraped ${uniqueNews.length} HKEX news items`);
      console.log(uniqueNews.map(item => `${item.date} - ${item.title} (${item.url})`).join('\n'));
      return uniqueNews.slice(0, 20); // Return top 20 items
      
    } catch (error) {
      log.error('Error scraping HKEX news:', error);
      throw new Error(`Failed to scrape HKEX news: ${error.message}`);
    }
  }

  async takeScreenshot(page: Page): Promise<string> {
    try {
      // Navigate to the page
      await page.goto('https://www.hkex.com.hk/News/News-Release?sc_lang=en', {
        waitUntil: 'networkidle',
        timeout: 30000,
      });

      // Wait for content to load
      await page.waitForTimeout(2000);

      // Take screenshot
      const screenshot = await page.screenshot({
        fullPage: true,
        type: 'png',
      });

      return `data:image/png;base64,${screenshot.toString('base64')}`;
    } catch (error) {
      log.error('Error taking HKEX screenshot:', error);
      throw new Error(`Failed to take screenshot: ${error.message}`);
    }
  }
}
