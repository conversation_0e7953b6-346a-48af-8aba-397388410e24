export const sleep = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export async function requestBrowseService(payload: {
  host: string;
  url: string;
}) {
  const headers = {
    'Content-Type': 'application/json',
    Accept: 'application/json',
    Authorization: 'Bearer <token>',
  };

  try {
    const response = await fetch(payload.host, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        url: payload.url,
      }),
    });

    try {
      const data = await response.json();
      console.log('Response:', data.data);
      return data.data;
    } catch (error) {
      console.log(response.text());
      return null;
    }
  } catch (error) {
    console.error('Error:', error);
    return null;
  }
}